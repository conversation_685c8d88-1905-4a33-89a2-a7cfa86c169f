# CLAUDE.md

本文档为 Claude Code (claude.ai/code) 在处理此代码库时提供指导。

## 项目概述

**sf-mall-manage** 是一个基于 Spring Boot 的 Java 8 微服务项目，用于管理商城业务系统。该项目提供了完整的商城管理功能，包括商品管理、订单管理、库存管理、支付管理、售后服务等核心业务模块，支持多租户架构和实时数据同步。

## 架构设计

项目采用分层领域驱动设计（DDD）架构，包含 6 个 Maven 模块：

- **starter/** (启动模块): Spring Boot 应用程序入口和配置
- **application/**: 用例实现、控制器、调度器、消息消费者
- **domain/**: 业务逻辑、实体、仓储接口
- **infrastructure/**: 仓储实现、数据库映射器、转换器
- **facade/**: 对外 API 契约和 DTO
- **common/**: 共享常量、枚举、工具类、查询对象

## 构建和运行命令

### Maven 命令
```bash
# 构建整个项目
mvn clean compile

# 运行测试
mvn test

# 构建可执行 JAR
mvn clean package -DskipTests

# 本地运行应用
mvn spring-boot:run -pl starter

# 单模块测试
mvn test -pl domain
```

### 本地开发
```bash
# 使用开发环境配置启动
mvn spring-boot:run -pl starter -Dspring.profiles.active=dev

# 打包部署
mvn clean package -DskipTests && java -jar starter/target/mall-manage-starter-*.jar
```

## 测试
- **单元测试**: 位于 `starter/src/test/java/` 目录
- **集成测试**: 使用 Spring Boot 测试注解 `@SpringBootTest`
- **测试文件命名**: 遵循 `*Test.java` 模式
- **常见测试包**: 应用服务、支付路由、消息处理器

## 核心依赖和版本
- **Spring Boot**: 2.3.1.RELEASE
- **MySQL Connector**: 8.0.23
- **Druid**: 1.1.20 (数据库连接池)
- **RocketMQ**: 2.1.1 (消息队列)
- **Redis**: Redisson 3.11.1
- **MapStruct**: 1.5.5.Final (实体映射)
- **Lombok**: 1.18.2
- **FastJSON**: 1.2.83
- **Nacos**: 0.2.12 (配置中心)
- **Hutool**: 5.8.11 (工具库)

## 目录结构模式
```
src/main/java/net/summerfarm/manage/
├── starter/
│   └── Application.java
├── application/
│   ├── inbound/        # 控制器、调度器、消费者
│   ├── service/        # 用例实现
│   └── wrapper/        # API 响应包装器
├── domain/
│   ├── order/          # 订单领域业务逻辑
│   ├── payment/        # 支付领域业务逻辑
│   └── entity/         # 领域实体
├── infrastructure/
│   ├── config/         # 基础设施配置
│   ├── repository/     # 仓储实现
│   ├── mapper/         # MyBatis 映射器
│   ├── model/          # 数据库模型
│   └── converter/      # 实体 ↔ DTO 转换
├── facade/             # API 契约
│   ├── dto/            # 数据传输对象
│   └── converter/      # DTO ↔ 领域转换
└── common/
    ├── constants/       # 应用常量
    ├── enums/          # 枚举类型
    ├── config/         # 通用配置
    └── middleware/     # 中间件操作
```

## 核心业务概念

### 商城业务流程
- **Orders**: 订单管理，支持多种订单类型（普通订单、团购订单、预售订单等）
- **Products**: 商品基础信息管理
- **Payment**: 支付渠道和路由管理
- **AfterSale**: 售后服务处理

### 订单类型
- **普通订单**: 业务编号 "01"
- **省心送订单**: 业务编号 "02"
- **补运费订单**: 业务编号 "03"
- **代下单**: 业务编号 "04"
- **预售订单**: 业务编号 "05"
- **直发采购订单**: 业务编号 "11"

### 数据流
1. 数据库变更 → RocketMQ → DTS 消费者
2. 消费者处理业务逻辑更新
3. 应用服务查询和处理业务请求
4. 定时任务进行数据同步和清理

## 配置文件
- 主配置: `starter/src/main/resources/application.yml`
- 环境配置: `application-dev.yml`, `application-qa.yml`, `application-online.yml`
- 日志配置: `logback-spring.xml`
- Nacos 动态配置: 通过 `NacosPropertiesHolder` 管理

## 常见开发任务

### 添加新实体
1. 在 `domain/src/main/java/[子包]/entity/` 添加实体
2. 在 `domain/src/main/java/[子包]/` 创建仓储接口
3. 在 `infrastructure/src/main/java/[子包]/repository/` 实现仓储
4. 在 `infrastructure/src/main/java/mapper/` 添加映射器
5. 在 `infrastructure/src/main/java/converter/` 添加转换器

### 添加新的业务功能
1. 在 `common/src/main/java/constants/` 创建相关常量
2. 在 `domain/src/main/java/[子包]/` 添加服务方法
3. 在 `infrastructure/src/main/java/repository/` 实现数据访问逻辑
4. 更新应用服务接口
5. 如需要，添加控制器端点

### 处理消息队列变更
1. 在 `application/src/main/java/inbound/message/` 找到相关处理器
2. 在 `common/src/main/java/constants/RocketMqConstant.java` 定义主题和标签
3. 实现事件处理逻辑
4. 将数据库变更映射到业务更新

## 环境配置
- **MySQL**: 标准 MySQL 8.x，使用 Druid 连接池
- **Redis**: 用于缓存会话和集群信息
- **RocketMQ**: 消息队列服务，端口通常为 9876
- **Nacos**: 配置中心和服务发现

## 本地测试
```bash
# 测试数据库连接
# 检查 application-dev.yml 中的数据库配置

# 测试 Redis 连接
# 确保 Redis 服务运行在配置的端口

# 测试消息队列
# 确保 RocketMQ 服务正常运行
```

## 部署说明
- **JAR 打包**: 生成可执行的 Spring Boot JAR
- **环境配置**: 使用 `-Dspring.profiles.active=[dev|qa|online]`
- **日志位置**: 通过 logback-spring.xml 配置
- **服务发现**: 使用 Nacos 进行配置管理

## 关键文件参考
- **领域逻辑**: `domain/src/main/java/order/*Service.java`
- **支付处理**: `application/src/main/java/inbound/provider/payment/`
- **配置类**: `starter/src/main/java/config/` 包
- **消息处理**: `application/src/main/java/service/dts/`
- **常量定义**: `common/src/main/java/constants/`

## 数据库表监听
项目通过 DTS (Data Transmission Service) 监听以下关键表的变更：
- **after_sale_order**: 售后订单表
- **orders**: 订单表
- **delivery_plan**: 配送计划表
- **cross_biz_goods_mapping**: 跨业务商品映射表

## 支付系统
项目集成了完整的支付路由系统，支持：
- 多支付渠道管理
- 支付场景配置
- 支付路由规则
- 支付结果处理

## 注意事项
- 项目使用 FastJSON 而非默认的 Jackson 进行 JSON 序列化
- 支持多租户架构，默认租户 ID 为 1
- 使用 MapStruct 进行实体映射，提高性能
- 集成钉钉机器人用于告警通知
- 支持微信公众号和小程序集成
