package net.summerfarm.manage.application.service.merchant;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelPageQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantCancelVO;


/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:30:16
 */
public interface MerchantCancelQueryService {

    /**
     * @description: 分页获取门店注销列表
     * @author: lzh
     * @date: 2023/4/20 17:47
     * @param: [merchantCancelPageQuery]
     * @return: com.github.pagehelper.PageInfo<net.summerfarm.model.vo.MerchantCancelVO>
     **/
    PageInfo<MerchantCancelVO> getPage(MerchantCancelPageQueryInput input);

    /**
     * @description: 获取门店注销申请详情
     * @author: lzh
     * @date: 2023/4/20 17:47
     * @param: [merchantCancelReq]
     * @return: net.summerfarm.model.vo.MerchantCancelVO
     **/
    MerchantCancelVO getDetail(MerchantCancelQueryInput input);

    /**
     * @description: 校验门店是否可以注销
     * @author: lzh
     * @date: 2023/4/20 17:47
     * @param: [merchantCancelReq]
     * @return: java.lang.Boolean
     **/
    MerchantCancelVO check(MerchantCancelQueryInput input);

    /**
     * @description: 获取门店注销申请基本信息
     * @author: lzh
     * @date: 2023/4/20 17:47
     * @param: [merchantCancelReq]
     * @return: net.summerfarm.model.vo.MerchantCancelVO
     **/
    MerchantCancelVO getInfo(MerchantCancelQueryInput input);


}
