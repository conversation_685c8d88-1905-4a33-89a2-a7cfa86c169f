package net.summerfarm.manage.application.service.merchant.impl;


import com.google.common.base.Joiner;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.manage.application.inbound.controller.merchant.input.command.MerchantAccountTransferCommandInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantTransferCheckInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantTransferCheckVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.service.merchant.MerchantAccountTransferQueryService;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.enums.MerchantEnums;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantAccountTransferQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantAccountTransferQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.assembler.MerchantAccountTransferAssembler;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.merchant.temp.MerchantTempRepository;
import net.summerfarm.manage.facade.merchant.MerchantCancelFacade;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
*
* <AUTHOR>
* @date 2024-01-10 14:07:22
* @version 1.0
*
*/
@Service
public class MerchantAccountTransferQueryServiceImpl implements MerchantAccountTransferQueryService {

    @Autowired
    private MerchantAccountTransferQueryRepository merchantAccountTransferQueryRepository;
    @Autowired
    private AreaQueryRepository areaQueryRepository;
    @Autowired
    private FollowUpRelationRepository followUpRelationRepository;
    @Autowired
    private MerchantTempRepository merchantTempRepository;
    @Resource
    private MerchantCancelFacade merchantCancelFacade;

    @Override
    public PageInfo<MerchantAccountTransferEntity> getPage(MerchantAccountTransferQueryInput input) {
        MerchantAccountTransferQueryParam queryParam = MerchantAccountTransferAssembler.toMerchantAccountTransferQueryParam(input);
        return merchantAccountTransferQueryRepository.getPage(queryParam);
    }

    @Override
    public MerchantAccountTransferEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return merchantAccountTransferQueryRepository.selectById(id);
    }

    @Override
    public MerchantTransferCheckVO check(MerchantTransferCheckInput checkInput) {
        MerchantTransferCheckVO mainVO = check(checkInput.getMId(),true);
        if (checkInput.getTransferMId() != null) {
            MerchantTransferCheckVO merchantVO = check(checkInput.getTransferMId(),false);
            if (!Objects.equals(merchantVO.getAreaNo(), mainVO.getAreaNo())) {
                throw new BizException("主门店和子门店区域不同");
            }
            return merchantVO;
        }
        return mainVO;
    }



    @Override
    public MerchantTransferCheckVO check(Long mid, Boolean isMainMerchant) {
        MerchantCancelInputReq req = new MerchantCancelInputReq();
        req.setMId(mid);
        List<String> check = new ArrayList<>();
        MerchantEntity merchantEntity = merchantTempRepository.getMerchantByMid(mid);
        if (merchantEntity == null) {
            throw new BizException("门店不存在或没通过审核");
        }
        if (Objects.equals(Global.BIG_MERCHANT, merchantEntity.getSize())) {
            check.add("为大客户");
        }
        if (!isMainMerchant){
            check.addAll(merchantCancelFacade.check(req));
        }
        if (Objects.equals(merchantEntity.getIslock(), MerchantEnums.MerchantIsLockEnum.CANCELLED.getCode())){
            check.add("已被迁移");
        }
        if (Objects.equals(merchantEntity.getIslock(), MerchantEnums.MerchantIsLockEnum.BLACK.getCode())){
            check.add("已被拉黑");
        }
        if (Objects.equals(merchantEntity.getIslock(), MerchantEnums.MerchantIsLockEnum.AUDIT.getCode())){
            check.add("在审核中");
        }
        if (!CollectionUtils.isEmpty(check)) {
            throw new BizException("门店" + Joiner.on(";").join(check) + "不支持迁移");
        }


        MerchantTransferCheckVO checkVO = MerchantAccountTransferAssembler.toMerchantTransferCheckVO(merchantEntity);
        // bd
        FollowUpRelationEntity oldFollow = followUpRelationRepository.selectOne(FollowUpRelationEntity.builder().mId(mid).build());
        checkVO.setAdminName(oldFollow == null  || oldFollow.getReassign() ? "" : oldFollow.getAdminName());
        // 区域
        Area area = areaQueryRepository.selectByAreaNo(merchantEntity.getAreaNo());
        if (area != null) {
            checkVO.setAreaName(area.getAreaName());
        }
        return checkVO;
    }
}