package net.summerfarm.manage.application.service.merchant.converter;

import net.summerfarm.manage.application.inbound.controller.merchant.input.MajorMerchantQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.XmMerchantStorePageResp;

import java.util.*;

public class XmMerchantStorePageReqConvert {


    private XmMerchantStorePageReqConvert() {
        // 无需实现
    }



    public static MerchantStorePageQueryReq toMerchantStorePageQueryReq(MajorMerchantQueryInput merchantQueryInput) {
        if (merchantQueryInput == null) {
            return null;
        }
        MerchantStorePageQueryReq merchantStorePageQueryReq = new MerchantStorePageQueryReq();
        merchantStorePageQueryReq.setAdminId(merchantQueryInput.getAdminId());
        merchantStorePageQueryReq.setPageIndex(merchantQueryInput.getPageIndex());
        merchantStorePageQueryReq.setPageSize(merchantQueryInput.getPageSize());
        merchantStorePageQueryReq.setType(merchantQueryInput.getType());
        merchantStorePageQueryReq.setStatus(merchantQueryInput.getStatus());
        merchantStorePageQueryReq.setSize(merchantQueryInput.getSize());
        merchantStorePageQueryReq.setDirect(merchantQueryInput.getDirect());
        merchantStorePageQueryReq.setExcludeStatusList(merchantQueryInput.getExcludeStatusList());
        return merchantStorePageQueryReq;

    }
}
