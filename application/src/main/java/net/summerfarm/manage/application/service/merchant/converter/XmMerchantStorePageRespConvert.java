package net.summerfarm.manage.application.service.merchant.converter;

import net.summerfarm.manage.application.inbound.controller.merchant.vo.MajorCustomerMerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.merchant.resp.XmMerchantStorePageResp;

import java.util.*;

public class XmMerchantStorePageRespConvert {


    private XmMerchantStorePageRespConvert() {
        // 无需实现
    }

    public static List<MerchantVO> toMerchantVOList(List<XmMerchantStorePageResp> xmMerchantStorePageRespList) {
        if (xmMerchantStorePageRespList == null) {
            return Collections.emptyList();
        }
        List<MerchantVO> merchantVOList = new ArrayList<>();
        for (XmMerchantStorePageResp xmMerchantStorePageResp : xmMerchantStorePageRespList) {
            merchantVOList.add(toMerchantVO(xmMerchantStorePageResp));
        }
        return merchantVOList;
    }

    public static MerchantVO toMerchantVO(XmMerchantStorePageResp xmMerchantStorePageResp) {
        if (xmMerchantStorePageResp == null) {
            return null;
        }
        MerchantVO merchantVO = new MerchantVO();
        merchantVO.setMId(xmMerchantStorePageResp.getMId());
        merchantVO.setRegisterTime(xmMerchantStorePageResp.getRegisterTime());
        merchantVO.setChannelCode(xmMerchantStorePageResp.getChannelCode());
        merchantVO.setAuditTime(xmMerchantStorePageResp.getAuditTime());
        merchantVO.setProvince(xmMerchantStorePageResp.getProvince());
        merchantVO.setCity(xmMerchantStorePageResp.getCity());
        merchantVO.setArea(xmMerchantStorePageResp.getArea());
        merchantVO.setAreaNo(xmMerchantStorePageResp.getAreaNo());
        merchantVO.setUpdateTime(xmMerchantStorePageResp.getUpdateTime());
        merchantVO.setStoreId(xmMerchantStorePageResp.getId());
        merchantVO.setMname(xmMerchantStorePageResp.getStoreName());
        merchantVO.setStatus(xmMerchantStorePageResp.getStatus());
        merchantVO.setRegisterTime(xmMerchantStorePageResp.getRegisterTime());
        merchantVO.setType(MerchantStoreEnums.Type.getDesc(xmMerchantStorePageResp.getType()));
        merchantVO.setAdminId(xmMerchantStorePageResp.getAdminId());
        merchantVO.setBusinessType(xmMerchantStorePageResp.getBusinessType());
        Arrays.stream(RegionalOrganizationEnums.Size.values()).filter(it -> Objects.equals(xmMerchantStorePageResp.getSize(), it.getCode())
        ).findFirst().ifPresent(size -> merchantVO.setSize(size.getDesc()));
        merchantVO.setPoiNote(xmMerchantStorePageResp.getPoiNote());
        merchantVO.setOperateStatus(xmMerchantStorePageResp.getOperateStatus());
// Not mapped TO fields:
// roleId
// mname
// mcontact
// openid
// phone
// islock
// rankId
// loginTime
// invitecode
// inviterChannelCode
// auditUser
// businessLicense
// address
// poiNote
// remark
// shopSign
// otherProof
// lastOrderTime
// size
// type
// tradeArea
// tradeGroup
// unionid
// mpOpenid
// adminId
// direct
// server
// popView
// memberIntegral
// grade
// skuShow
// rechargeAmount
// cashAmount
// cashUpdateTime
// showPrice
// mergeAdmin
// mergeTime
// firstLoginPop
// changePop
// pullBlackRemark
// pullBlackOperator
// houseNumber
// companyBrand
// cluePool
// merchantType
// enterpriseScale
// examineType
// displayButton
// operateStatus
// updater
// doorPic
// preRegisterFlag
// adminRealName
// areaName
// invoiceTitle
// Not mapped FROM fields:
// id
// tenantId
// storeName
// type
// status
// createTime
// businessType
// regionalId
// adminId
// size
        return merchantVO;
    }






    public static MajorCustomerMerchantVO toMajorCustomerMerchantVO(XmMerchantStorePageResp merchantStoreResultResp) {
        if (merchantStoreResultResp == null) {
            return null;
        }
        MajorCustomerMerchantVO majorCustomerMerchantVO = new MajorCustomerMerchantVO();
        majorCustomerMerchantVO.setMId(merchantStoreResultResp.getMId());
        majorCustomerMerchantVO.setType(merchantStoreResultResp.getType());
        majorCustomerMerchantVO.setStatus(merchantStoreResultResp.getStatus());
        majorCustomerMerchantVO.setMname(merchantStoreResultResp.getStoreName());
        majorCustomerMerchantVO.setAreaNo(merchantStoreResultResp.getAreaNo());
        majorCustomerMerchantVO.setStoreId(merchantStoreResultResp.getId());
        return majorCustomerMerchantVO;
    }
    public static List<MajorCustomerMerchantVO> toMajorCustomerMerchantVOList(List<XmMerchantStorePageResp> xmMerchantStorePageRespList) {
        if (xmMerchantStorePageRespList == null) {
            return Collections.emptyList();
        }
        List<MajorCustomerMerchantVO> merchantVOList = new ArrayList<>();
        for (XmMerchantStorePageResp xmMerchantStorePageResp : xmMerchantStorePageRespList) {
            merchantVOList.add(toMajorCustomerMerchantVO(xmMerchantStorePageResp));
        }
        return merchantVOList;
    }
}
