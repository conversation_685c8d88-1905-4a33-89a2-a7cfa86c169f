package net.summerfarm.manage.application.service.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.multipart.MultipartFile;

public interface MerchantCommandService {

    MerchantEntity addMerchant(MerchantEntity entity);

    CommonResult<String> saveBatch(MultipartFile file, boolean isInitContact);


}
