package net.summerfarm.manage.application.service.marketItem.helper;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.goods.client.enums.GoodsStorageLocationEnum;
import net.summerfarm.goods.client.enums.GuaranteeUnitEnum;
import net.summerfarm.goods.client.enums.PlaceTypeEnum;
import net.summerfarm.goods.client.resp.ProductsPropertyValueResp;
import net.summerfarm.manage.common.enums.InventoryExtTypeEnum;
import net.summerfarm.manage.common.enums.VolumeUnitEnums;
import net.summerfarm.manage.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.manage.facade.wiki.WikiKnowledgeFacade;
import net.summerfarm.manage.facade.wiki.dto.SearchResultItem;
import net.summerfarm.manage.facade.wiki.dto.ScoreItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 商品AI扩展信息助手类
 * 负责商品信息组装、消息拼接等功能
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Component
@Slf4j
public class MarketItemAiExtGoodsInfoHelper {

    @Autowired
    private WikiKnowledgeFacade wikiKnowledgeFacade;

    /**
     * 构建商品信息文本（只包含非空字段）- 使用GoodsInfoDTO
     */
    public String buildProductInfoText(GoodsInfoDTO goodsInfo) {
        StringBuilder sb = new StringBuilder();

        // 商品名称（必填）
        if (StringUtils.isNotBlank(goodsInfo.getTitle())) {
            sb.append("商品名称：").append(goodsInfo.getTitle()).append("\n");
        }

        // 基本信息
        appendAttributeIfNotBlank(sb, "规格", goodsInfo.getSpecification());
        appendAttributeIfNotBlank(sb, "产地", goodsInfo.getOrigin());
        appendAttributeIfNotBlank(sb, "品牌", goodsInfo.getBrandName());

        // 类目信息
//        if (StringUtils.isNotBlank(goodsInfo.getFirstCategory())) {
//            sb.append("类目：").append(goodsInfo.getFirstCategory());
//            if (StringUtils.isNotBlank(goodsInfo.getSecondCategory())) {
//                sb.append(" > ").append(goodsInfo.getSecondCategory());
//            }
//            if (StringUtils.isNotBlank(goodsInfo.getCategoryName())) {
//                sb.append(" > ").append(goodsInfo.getCategoryName());
//            }
//            sb.append("\n");
//        }

        // 商品描述
        appendAttributeIfNotBlank(sb, "主标题", goodsInfo.getTitle());
        appendAttributeIfNotBlank(sb, "副标题", goodsInfo.getSubTitle());
        appendAttributeIfNotBlank(sb, "SKU实物名称", goodsInfo.getSkuTitle());

        // 物理属性
        appendAttributeIfNotBlank(sb, "体积", goodsInfo.getVolume());
        appendAttributeIfNotBlank(sb, "体积单位", VolumeUnitEnums.getDescByVolumeUnitBym3Default(goodsInfo.getVolumeUnit()));

        if (goodsInfo.getWeight() != null) {
            sb.append("重量：").append(goodsInfo.getWeight()).append("\n");
        }
        if (goodsInfo.getNetWeightNum() != null) {
            sb.append("净重：").append(goodsInfo.getNetWeightNum());
            if (StringUtils.isNotBlank(goodsInfo.getNetWeightUnit())) {
                sb.append(goodsInfo.getNetWeightUnit());
            }
            sb.append("\n");
        }
        appendAttributeIfNotBlank(sb, "规格单位", goodsInfo.getSpecificationUnit());

        // 仓储信息
        appendAttributeIfNotBlank(sb, "存储温度", goodsInfo.getStorageTemperature());
        if (goodsInfo.getStorageLocation() != null) {
            String storageLocationText = getStorageLocationText(goodsInfo.getStorageLocation());
            appendAttributeIfNotBlank(sb, "储存区域", storageLocationText);
        }

        // 保质期信息
        if (goodsInfo.getGuaranteePeriod() != null) {
            sb.append("保质期：").append(goodsInfo.getGuaranteePeriod());
            if (goodsInfo.getGuaranteeUnit() != null) {
                String unit = getGuaranteeUnitText(goodsInfo.getGuaranteeUnit());
                sb.append(unit);
            }
            sb.append("\n");
        }

        // 预警时长
        if (goodsInfo.getWarnTimePeriodDay() != null) {
            sb.append("临期时长：").append(goodsInfo.getWarnTimePeriodDay()).append("天\n");
        }

        // 保鲜期
        if (goodsInfo.getFreshnessPeriodDay() != null) {
            sb.append("保鲜期：").append(goodsInfo.getFreshnessPeriodDay()).append("天\n");
        }

        // 商品性质
//        if (goodsInfo.getExtType() != null) {
//            String extTypeText = getExtTypeText(goodsInfo.getExtType());
//            appendAttributeIfNotBlank(sb, "SKU性质", extTypeText);
//        }

        // 代理类型
//        if (goodsInfo.getAgentType() != null) {
//            String agentTypeText = getAgentTypeText(goodsInfo.getAgentType());
//            appendAttributeIfNotBlank(sb, "代理类型", agentTypeText);
//        }

        // 地点类型
        if (goodsInfo.getPlaceType() != null) {
            String placeTypeText = getPlaceTypeText(goodsInfo.getPlaceType());
            appendAttributeIfNotBlank(sb, "来源地类型", placeTypeText);
        }

        // 买手信息
//        appendAttributeIfNotBlank(sb, "买手名称", goodsInfo.getBuyerName());

        // 自定义属性
        appendCustomProperties(sb, goodsInfo);

        return sb.toString();
    }

    /**
     * 构建搜索关键词
     */
    public String buildSearchKeywords(GoodsInfoDTO goodsInfo) {
        StringBuilder searchText = new StringBuilder();

        // 添加商品名称和sku编码
        if (StringUtils.isNotBlank(goodsInfo.getTitle())) {
            searchText.append(goodsInfo.getTitle()).append("_").append(goodsInfo.getSku());
        }

        return searchText.toString().trim();
    }

    /**
     * 组装知识库搜索结果
     */
    public String buildKnowledgeContent(String question, List<SearchResultItem> searchResults) {
        if (searchResults == null || searchResults.isEmpty()) {
            return "";
        }

        // 按评分排序，优先展示高质量的知识
        List<SearchResultItem> sortedResults = sortByScore(searchResults);
        if (sortedResults.isEmpty()) {
            return "";
        }

        StringBuilder knowledgeBuilder = new StringBuilder();
        int validCount = 1;
        for (SearchResultItem item : sortedResults) {
            String knowledgeText = formatKnowledgeItem(question, item);

            if (StringUtils.isNotBlank(knowledgeText)) {
                knowledgeBuilder.append("【知识").append(validCount).append("】");
                knowledgeBuilder.append("\n").append(knowledgeText).append("\n\n");
            }

            validCount++;
        }

        return knowledgeBuilder.toString().trim();
    }

    /**
     * 按评分对搜索结果进行排序
     */
    private List<SearchResultItem> sortByScore(List<SearchResultItem> searchResults) {
        return searchResults.stream()
                .sorted((item1, item2) -> {
                    Double score1 = getOverallScore(item1);
                    Double score2 = getOverallScore(item2);
                    // 降序排列，评分高的在前
                    return Double.compare(score2, score1);
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取综合评分
     */
    private Double getOverallScore(SearchResultItem item) {
        if (item.getScore() == null || item.getScore().isEmpty()) {
            return 0.0;
        }

        return item.getScore().stream()
                .map(ScoreItem::getValue)
                .max(Double::compareTo)
                .orElse(0.0);
    }

    /**
     * 格式化单个知识库条目
     */
    private String formatKnowledgeItem(String question, SearchResultItem item) {
        if (item == null) {
            return "";
        }

        StringBuilder itemBuilder = new StringBuilder();
        // 优先使用答案内容，因为答案通常更完整
        if (StringUtils.isNotBlank(item.getA()) || StringUtils.isNotBlank(item.getQ())) {
            itemBuilder.append("搜索问题:").append(question).append("\n");
            itemBuilder.append("答案:")
                    .append(StringUtils.isNotBlank(item.getA()) ? item.getA() : item.getQ()).append("\n");
            itemBuilder.append("源名称:").append(item.getSourceName()).append("\n");
            itemBuilder.append("评分情况(type:评分类型,value:评分值):")
                    .append(JSONObject.toJSONString(item.getScore())).append("\n");
        }

        return itemBuilder.toString();
    }

    /**
     * 构建完整的上下文信息
     */
    public String buildFullContext(String productInfoText, String knowledgeContext) {
        StringBuilder contextBuilder = new StringBuilder();

        // 添加商品信息
        contextBuilder.append("=== 商品基础信息 ===\n");
        if (StringUtils.isNotBlank(productInfoText)) {
            contextBuilder.append(productInfoText.trim());
        } else {
            contextBuilder.append("暂无商品信息");
        }

        // 添加相关知识（如果有）
        if (StringUtils.isNotBlank(knowledgeContext)) {
            contextBuilder.append("\n\n=== 相关专业知识 ===\n");
            contextBuilder.append(knowledgeContext.trim());
        } else {
            contextBuilder.append("\n\n=== 相关专业知识 ===\n");
            contextBuilder.append("暂无相关专业知识");
        }

        return contextBuilder.toString();
    }

    /**
     * 搜索相关知识库内容
     */
    public String searchRelatedKnowledge(GoodsInfoDTO goodsInfo) {
        try {
            // 构建搜索关键词
            String searchQuery = buildSearchKeywords(goodsInfo);
            if (StringUtils.isBlank(searchQuery)) {
                log.warn("searchRelatedKnowledge: 搜索关键词为空, sku={}, title={}",
                        goodsInfo.getSku(), goodsInfo.getTitle());
                return "";
            }

            log.info("searchRelatedKnowledge: 开始搜索知识库, sku={}, 关键词=[{}]",
                    goodsInfo.getSku(), searchQuery);

            // 调用Wiki知识库搜索
            List<SearchResultItem> searchResults = wikiKnowledgeFacade.searchWiki(searchQuery);

            if (searchResults == null || searchResults.isEmpty()) {
                log.info("searchRelatedKnowledge: 未找到相关知识, sku={}, 关键词=[{}]",
                        goodsInfo.getSku(), searchQuery);
                return "";
            }

            // 组装知识内容
            String knowledge = buildKnowledgeContent(searchQuery, searchResults);

            if (StringUtils.isNotBlank(knowledge)) {
                log.debug("searchRelatedKnowledge: 成功获取相关知识, sku={}, 原始结果数={}",
                        goodsInfo.getSku(), searchResults.size());
            } else {
                log.debug("searchRelatedKnowledge: 知识库返回结果但组装后为空, sku={}, 原始结果数={}",
                        goodsInfo.getSku(), searchResults.size());
            }

            return knowledge;

        } catch (Exception e) {
            log.error("searchRelatedKnowledge: 搜索知识库异常, sku={}, 商品名称={}",
                    goodsInfo.getSku(), goodsInfo.getTitle(), e);
            return "";
        }
    }

    private void appendAttributeIfNotBlank(StringBuilder sb, String label, String value) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(label).append("：").append(value).append("\n");
        }
    }

    /**
     * 获取保质期单位文本
     */
    private String getGuaranteeUnitText(Integer guaranteeUnit) {
        if (guaranteeUnit == null) {
            return "";
        }

        for (GuaranteeUnitEnum value : GuaranteeUnitEnum.values()) {
            if (value.getValue().equals(guaranteeUnit)) {
                return value.getContent();
            }
        }
        return "";
    }

    /**
     * 获取储存区域文本
     */
    private String getStorageLocationText(Integer storageLocation) {
        if (storageLocation == null) {
            return "";
        }

        for (GoodsStorageLocationEnum value : GoodsStorageLocationEnum.values()) {
            if (value.getValue().equals(storageLocation)) {
                return value.getContent();
            }
        }
        return "";
    }

    /**
     * 获取SKU性质文本
     */
    private String getExtTypeText(Integer extType) {
        if (extType == null) {
            return "";
        }

        for (InventoryExtTypeEnum value : InventoryExtTypeEnum.values()) {
            if (value.type().equals(extType)) {
                return value.getADesc();
            }
        }
        return "";
    }

    /**
     * 获取代理类型文本
     */
    private String getAgentTypeText(Integer agentType) {
        if (agentType == null) {
            return "";
        }

        for (AgentTypeEnum value : AgentTypeEnum.values()) {
            if (value.getType().equals(agentType)) {
                return value.getDesc();
            }
        }
        return "";
    }

    /**
     * 获取地点类型文本
     */
    private String getPlaceTypeText(Integer placeType) {
        if (placeType == null) {
            return "";
        }

        for (PlaceTypeEnum value : PlaceTypeEnum.values()) {
            if (value.getValue().equals(placeType)) {
                return value.getContent();
            }
        }
        return "";
    }

    /**
     * 添加自定义属性信息
     */
    private void appendCustomProperties(StringBuilder sb, GoodsInfoDTO goodsInfo) {
        try {
            if (!CollectionUtils.isEmpty(goodsInfo.getPropertyValueRespList())) {
                for (ProductsPropertyValueResp propertyValue : goodsInfo.getPropertyValueRespList()) {
                    if (propertyValue != null && propertyValue.getProductsPropertyName() != null &&
                            propertyValue.getProductsPropertyValue() != null) {
                        sb.append(propertyValue.getProductsPropertyName())
                                .append("：")
                                .append(propertyValue.getProductsPropertyValue()).append("\n");
                    }
                }
            }

            if (!CollectionUtils.isEmpty(goodsInfo.getSpuPropertyValueRespList())) {
                for (ProductsPropertyValueResp propertyValue : goodsInfo.getSpuPropertyValueRespList()) {
                    if (propertyValue != null && propertyValue.getProductsPropertyName() != null &&
                            propertyValue.getProductsPropertyValue() != null) {
                        sb.append(propertyValue.getProductsPropertyName())
                                .append("：")
                                .append(propertyValue.getProductsPropertyValue()).append("\n");
                    }
                }
            }
        } catch (Exception e) {
            log.error("appendCustomProperties: error processing custom properties", e);
        }
    }
}
