package net.summerfarm.manage.application.service.marketItem.dto;

import lombok.Data;

import java.util.List;

/**
 * 批量初始化结果
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class BatchInitResult {
    
    /**
     * 总数量
     */
    private int totalCount;
    
    /**
     * 成功数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failCount;
    
    /**
     * 跳过数量（已存在）
     */
    private int skipCount;
    
    /**
     * 成功的SKU列表
     */
    private List<String> successSkus;
    
    /**
     * 失败的SKU列表
     */
    private List<String> failSkus;
    
    /**
     * 跳过的SKU列表
     */
    private List<String> skipSkus;
    
    /**
     * 处理耗时（毫秒）
     */
    private long processingTimeMs;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    public BatchInitResult() {
        this.totalCount = 0;
        this.successCount = 0;
        this.failCount = 0;
        this.skipCount = 0;
    }
    
    public BatchInitResult(int totalCount) {
        this();
        this.totalCount = totalCount;
    }
    
    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return failCount == 0 && totalCount > 0;
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }
}
