package net.summerfarm.manage.application.service.marketItem.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.common.enums.MarketItemAiExtTypeEnum;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.constants.AppConsts;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.service.MarketItemAiExtCommandDomainService;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.ai.OpenAIChatFacade;
import net.summerfarm.manage.facade.goods.ProductFacade;
import net.summerfarm.manage.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.manage.facade.wiki.WikiKnowledgeFacade;
import net.summerfarm.manage.application.service.marketItem.helper.MarketItemAiExtGoodsInfoHelper;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Service
@Slf4j
public class MarketItemAiExtCommandServiceImpl implements MarketItemAiExtCommandService {

    @Autowired
    private MarketItemAiExtCommandDomainService marketItemAiExtCommandDomainService;

    @Autowired
    private MarketItemAiExtQueryRepository marketItemAiExtQueryRepository;

    @Autowired
    private InventoryQueryRepository inventoryQueryRepository;

    @Autowired
    private OpenAIChatFacade openAIChatFacade;

    @Autowired
    private WikiKnowledgeFacade wikiKnowledgeFacade;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Autowired
    private ProductFacade productFacade;

    @Autowired
    private MarketItemAiExtGoodsInfoHelper marketItemAiExtGoodsInfoHelper;


    @Override
    public MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildCreateParam(input);
        return marketItemAiExtCommandDomainService.insert(param);
    }


    @Override
    public int update(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildUpdateParam(input);
        return marketItemAiExtCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return marketItemAiExtCommandDomainService.delete(id);
    }

    /**
     * 解析AI响应，提取问题列表
     */
    private List<String> parseAIResponse(String aiResponse) {
        if (StringUtils.isBlank(aiResponse)) {
            return Collections.emptyList();
        }

        try {
            // 1. 清理响应内容，移除markdown标记
            String cleanedResponse = cleanAIResponse(aiResponse);

            // 2. 尝试直接解析JSON数组
            List<String> questions = JSON.parseArray(cleanedResponse, String.class);
            if (questions != null && !questions.isEmpty()) {
                // 过滤和清理问题
                return questions.stream()
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .filter(q -> q.length() > 3) // 过滤太短的问题
                        .collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.info("parseAIResponse: JSON解析失败，尝试手动提取", e);
        }

        // 3. 如果JSON解析失败，尝试手动提取问题
        return extractQuestionsManually(aiResponse);
    }

    /**
     * 清理AI响应内容，移除markdown等标记
     */
    private String cleanAIResponse(String aiResponse) {
        if (StringUtils.isBlank(aiResponse)) {
            return "";
        }

        String cleaned = aiResponse.trim();

        // 移除markdown代码块标记
        cleaned = cleaned.replaceAll("```json\\s*", "");
        cleaned = cleaned.replaceAll("```\\s*", "");

        // 移除可能的前缀文本
        cleaned = cleaned.replaceAll("^[^\\[]*\\[", "[");

        // 移除可能的后缀文本
        cleaned = cleaned.replaceAll("\\][^\\]]*$", "]");

        return cleaned.trim();
    }

    /**
     * 手动提取问题（当JSON解析失败时使用）
     */
    private List<String> extractQuestionsManually(String aiResponse) {
        List<String> questions = new ArrayList<>();

        try {
            String[] lines = aiResponse.split("\n");

            for (String line : lines) {
                String trimmedLine = line.trim();

                // 跳过空行和markdown标记
                if (StringUtils.isBlank(trimmedLine) ||
                        trimmedLine.startsWith("```") ||
                        trimmedLine.equals("[") ||
                        trimmedLine.equals("]")) {
                    continue;
                }

                // 提取问题内容
                String question = extractQuestionFromLine(trimmedLine);
                if (StringUtils.isNotBlank(question)) {
                    questions.add(question);
                }
            }

            // 如果按行提取失败，尝试正则表达式提取
            if (questions.isEmpty()) {
                questions = extractQuestionsWithRegex(aiResponse);
            }

        } catch (Exception e) {
            log.error("extractQuestionsManually: 手动提取问题失败 {}", JSONObject.toJSONString(aiResponse), e);
        }

        log.info("extractQuestionsManually: 手动提取到{}个问题", questions.size());
        return questions;
    }

    /**
     * 从单行中提取问题
     */
    private String extractQuestionFromLine(String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }

        // 移除JSON数组的引号和逗号
        String cleaned = line.replaceAll("^\\s*[\"']", "")
                .replaceAll("[\"']\\s*,?\\s*$", "")
                .trim();

        // 检查是否包含问号
        if (cleaned.contains("?") || cleaned.contains("？")) {
            // 移除可能的序号前缀
            cleaned = cleaned.replaceAll("^\\d+[.、]\\s*", "");

            if (cleaned.length() > 3) {
                return cleaned;
            }
        }

        return null;
    }

    /**
     * 使用正则表达式提取问题
     */
    private List<String> extractQuestionsWithRegex(String aiResponse) {
        List<String> questions = new ArrayList<>();

        try {
            // 匹配引号包围的问题
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\"([^\"]*[?？][^\"]*)\"|'([^']*[?？][^']*)'");
            java.util.regex.Matcher matcher = pattern.matcher(aiResponse);

            while (matcher.find()) {
                String question = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
                if (StringUtils.isNotBlank(question) && question.trim().length() > 3) {
                    questions.add(question.trim());
                }
            }

        } catch (Exception e) {
            log.error("extractQuestionsWithRegex: 正则提取失败", e);
        }

        return questions;
    }

    @Override
    public BatchInitResult batchInitMarketItemQuestions(List<String> skus) {
        return processBatchInit(skus, false);
    }

    @Override
    public BatchInitResult refreshMarketItemQuestions(List<String> skus) {
        return processBatchInit(skus, true);
    }

    /**
     * 处理批量初始化或刷新
     *
     * @param skus         SKU列表
     * @param forceRefresh 是否强制刷新（跳过已存在检查）
     * @return 处理结果
     */
    private BatchInitResult processBatchInit(List<String> skus, boolean forceRefresh) {
        long startTime = System.currentTimeMillis();

        // 如果SKU列表为空，自动查询需要处理的SKU
        if (CollectionUtils.isEmpty(skus)) {
            log.info("processBatchInit: sku list is empty, querying skus that need AI questions");
            throw new BizException("sku list is empty");
        }

        // skus限制2000
        if (skus.size() > 2000) {
            log.error("processBatchInit: sku list size exceeds limit, size={}", skus.size());
            throw new BizException("sku list size exceeds limit");
        }

        BatchInitResult result = new BatchInitResult(skus.size());
        result.setSuccessSkus(Lists.newArrayList());
        result.setFailSkus(Lists.newArrayList());
        result.setSkipSkus(Lists.newArrayList());

        try {
            List<String> filteredSkus;

            if (forceRefresh) {
                // 强制刷新模式：处理所有SKU，更新已存在的记录
                log.info("processBatchInit: force refresh mode, will update existing data");
                filteredSkus = skus;
            } else {
                // 1. 批量检查已存在的数据，过滤掉已有问题的SKU
                filteredSkus = filterExistingSkus(skus, result);
                if (filteredSkus.isEmpty()) {
                    log.info("processBatchInit: all skus already have questions, skip processing");
                    result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                    return result;
                }
            }

            // 2. 批量查询商品基础数据 - 使用ProductFacade#listGoodsInfoBySkus
            Map<String, GoodsInfoDTO> goodsInfoMap = new HashMap<>();
            try {
                List<GoodsInfoDTO> goodsInfoList = productFacade.listGoodsInfoBySkus(filteredSkus);
                for (GoodsInfoDTO goodsInfo : goodsInfoList) {
                    goodsInfoMap.put(goodsInfo.getSku(), goodsInfo);
                }

                // 检查哪些SKU没有查到数据
                for (String sku : filteredSkus) {
                    if (!goodsInfoMap.containsKey(sku)) {
                        result.getFailSkus().add(sku);
                        result.setFailCount(result.getFailCount() + 1);
                        log.warn("processBatchInit: no goods info found for sku {}", sku);
                    }
                }
            } catch (Exception e) {
                log.error("processBatchInit: error querying goods info", e);
                // 将所有SKU标记为失败
                for (String sku : filteredSkus) {
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                }
            }

            if (goodsInfoMap.isEmpty()) {
                log.warn("processBatchInit: no valid goods info found");
                result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                return result;
            }

            // 3. 批量生成AI问题并保存
            batchGenerateAndSaveQuestionsWithGoodsInfo(goodsInfoMap, result, forceRefresh);

        } catch (Exception e) {
            log.error("processBatchInit: error processing batch", e);
            result.setErrorMessage("批量处理异常: " + e.getMessage());
        }

        result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        log.info("processBatchInit: completed, mode={}, total={}, success={}, fail={}, skip={}, time={}ms",
                forceRefresh ? "REFRESH" : "INIT", result.getTotalCount(), result.getSuccessCount(),
                result.getFailCount(), result.getSkipCount(), result.getProcessingTimeMs());

        return result;
    }

    /**
     * 过滤已存在问题的SKU
     */
    private List<String> filterExistingSkus(List<String> skus, BatchInitResult result) {
        List<String> filteredSkus = Lists.newArrayList();

        // 分批查询，避免IN条件过长
        int batchSize = 100;
        for (int i = 0; i < skus.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skus.size());
            List<String> batchSkus = skus.subList(i, endIndex);

            try {
                // 使用批量查询优化性能
                List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.batchSelectBySkusAndExtType(
                        batchSkus, MarketItemAiExtTypeEnum.RELATION_QUESTION.getCode());
                Set<String> existingSkus = existingData.stream()
                        .map(MarketItemAiExtEntity::getSku)
                        .collect(Collectors.toSet());

                for (String sku : batchSkus) {
                    if (existingSkus.contains(sku)) {
                        result.getSkipSkus().add(sku);
                        result.setSkipCount(result.getSkipCount() + 1);
                    } else {
                        filteredSkus.add(sku);
                    }
                }
            } catch (Exception e) {
                log.error("filterExistingSkus: error querying batch {}", batchSkus, e);
                // 如果批量查询失败，将这批SKU标记为失败
                for (String sku : batchSkus) {
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                }
            }
        }

        return filteredSkus;
    }

    /**
     * 批量生成AI问题并保存 - 使用GoodsInfoDTO
     */
    private void batchGenerateAndSaveQuestionsWithGoodsInfo(Map<String, GoodsInfoDTO> goodsInfoMap, BatchInitResult result, boolean forceRefresh) {
        for (Map.Entry<String, GoodsInfoDTO> entry : goodsInfoMap.entrySet()) {
            String sku = entry.getKey();
            GoodsInfoDTO goodsInfo = entry.getValue();

            try {
                // 构建商品信息文本
                String productInfoText = marketItemAiExtGoodsInfoHelper.buildProductInfoText(goodsInfo);

                // 搜索相关知识库内容
                String knowledgeContext = marketItemAiExtGoodsInfoHelper.searchRelatedKnowledge(goodsInfo);

                // 构建完整的上下文信息
                String fullContext = marketItemAiExtGoodsInfoHelper.buildFullContext(productInfoText, knowledgeContext);

                // 调用AI生成问题
                String prompt = nacosPropertiesHolder.getGoodsRelationQuestionPrompt();
                String aiResponse = openAIChatFacade.chat(fullContext, prompt);

                if (StringUtils.isBlank(aiResponse)) {
                    log.warn("batchGenerateAndSaveQuestionsWithGoodsInfo: AI response is blank for sku {}", sku);
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                    continue;
                }

                // 解析AI响应
                List<String> questions = parseAIResponse(aiResponse);
                if (questions.isEmpty()) {
                    log.warn("batchGenerateAndSaveQuestionsWithGoodsInfo: no valid questions parsed for sku {}", sku);
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                    continue;
                }

                // 保存问题
                String questionsJson = JSON.toJSONString(questions);
                saveOrUpdateAiQuestions(sku, goodsInfo, questionsJson, forceRefresh);

                result.getSuccessSkus().add(sku);
                result.setSuccessCount(result.getSuccessCount() + 1);
                log.debug("batchGenerateAndSaveQuestionsWithGoodsInfo: success for sku {}, questions count: {}", sku, questions.size());

            } catch (Exception e) {
                log.error("batchGenerateAndSaveQuestionsWithGoodsInfo: error processing sku {}", sku, e);
                result.getFailSkus().add(sku);
                result.setFailCount(result.getFailCount() + 1);
            }
        }
    }

    /**
     * 保存或更新AI问题
     */
    private void saveOrUpdateAiQuestions(String sku, GoodsInfoDTO goodsInfo, String questionsJson, boolean forceRefresh) {
        if (forceRefresh) {
            // 强制刷新模式：查找已存在的记录并更新
            List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.batchSelectBySkusAndExtType(
                    Collections.singletonList(sku), MarketItemAiExtTypeEnum.RELATION_QUESTION.getCode());
            if (!existingData.isEmpty()) {
                // 更新已存在的记录
                MarketItemAiExtEntity existingEntity = existingData.get(0);
                MarketItemAiExtCommandParam updateParam = new MarketItemAiExtCommandParam();
                updateParam.setId(existingEntity.getId());
                updateParam.setExtValue(questionsJson);
                updateParam.setUpdateTime(LocalDateTime.now());

                marketItemAiExtCommandDomainService.update(updateParam);
                log.debug("saveOrUpdateAiQuestions: updated existing record for sku {}", sku);
            } else {
                // 如果没有找到已存在的记录，则插入新记录
                insertNewRecordWithGoodsInfo(sku, goodsInfo, questionsJson);
                log.debug("saveOrUpdateAiQuestions: inserted new record for sku {} (not found in refresh mode)", sku);
            }
        } else {
            // 初始化模式：直接插入新记录
            insertNewRecordWithGoodsInfo(sku, goodsInfo, questionsJson);
        }
    }

    /**
     * 插入新记录 - 使用GoodsInfoDTO
     */
    private void insertNewRecordWithGoodsInfo(String sku, GoodsInfoDTO goodsInfo, String questionsJson) {
        MarketItemAiExtCommandParam param = new MarketItemAiExtCommandParam();
        param.setSku(sku);
        param.setPdId(goodsInfo.getSkuPdId());
        param.setExtType(MarketItemAiExtTypeEnum.RELATION_QUESTION.getCode()); // 商品相关问题
        param.setExtValue(questionsJson);
        param.setTenantId(AppConsts.XIANMU_TENANT_ID);
        param.setCreateTime(LocalDateTime.now());
        param.setUpdateTime(LocalDateTime.now());

        marketItemAiExtCommandDomainService.insert(param);
    }

    @Override
    public Map<String, String> getProductInfoTextBySkus(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.emptyMap();
        }
        if (skus.stream().noneMatch(Objects::nonNull)){
            throw new IllegalArgumentException("skus must not be null");
        }

        try {
            List<GoodsInfoDTO> goodsInfoList = productFacade.listGoodsInfoBySkus(
                    skus);
            if (goodsInfoList.isEmpty()) {
                return Collections.emptyMap();
            }

            return goodsInfoList.stream()
                    .collect(Collectors.toMap(
                            GoodsInfoDTO::getSku,
                            goodsInfo -> marketItemAiExtGoodsInfoHelper.buildProductInfoText(goodsInfo)));

        } catch (Exception e) {
            log.error("getProductInfoTextBySkus: error for skus {}", JSONObject.toJSONString(skus), e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, String> getSearchKeywordsBySkus(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.emptyMap();
        }
        if (skus.stream().noneMatch(Objects::nonNull)){
            throw new IllegalArgumentException("skus must not be null");
        }

        try {
            List<GoodsInfoDTO> goodsInfoList = productFacade.listGoodsInfoBySkus(
                    skus);
            return goodsInfoList.parallelStream()
                   .collect(Collectors.toMap(
                            GoodsInfoDTO::getSku,
                            goodsInfo -> marketItemAiExtGoodsInfoHelper.searchRelatedKnowledge(goodsInfo)));

        } catch (Exception e) {
            log.error("testProductInfoText: error for sku {}", JSONObject.toJSONString(skus), e);
            return  Collections.emptyMap();
        }
    }
}
