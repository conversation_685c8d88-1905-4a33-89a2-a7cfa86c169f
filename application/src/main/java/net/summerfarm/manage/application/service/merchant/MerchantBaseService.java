package net.summerfarm.manage.application.service.merchant;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantContactAddressRemarkVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactVO;
import net.summerfarm.manage.application.service.merchant.converter.XmMerchantStorePageRespConvert;
import net.summerfarm.manage.application.service.merchant.converter.XmMerchantStoreRespConvert;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.enums.*;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.service.AdminDomainService;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.service.AreaService;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.service.FollowUpRelationDomainService;
import net.summerfarm.manage.domain.invoice.entity.InvoiceConfig;
import net.summerfarm.manage.domain.invoice.service.InvoiceConfigService;
import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantOuterEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantPropertiesExtQueryParam;
import net.summerfarm.manage.domain.merchant.repository.ContactRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantOuterRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.facade.deliivery.dto.ContactBelongFenceDTO;
import net.summerfarm.manage.facade.deliivery.input.ContactBelongFenceReq;
import net.summerfarm.manage.facade.deliivery.input.ContactFenceQueryInput;
import net.summerfarm.manage.facade.merchant.converter.MerchantPageQueryConverter;
import net.summerfarm.manage.facade.merchant.converter.MerchantQueryConverter;
import net.summerfarm.manage.facade.deliivery.dto.DistributionRulesDTO;
import net.summerfarm.manage.facade.merchant.MerchantAccountFacade;
import net.summerfarm.manage.facade.merchant.ContactFacade;
import net.summerfarm.manage.facade.merchant.MerchantQueryFacade;
import net.summerfarm.manage.facade.fence.FenceQueryFacade;
import net.summerfarm.manage.facade.deliivery.input.DistributionRulesInfoInput;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.common.utils.AuthUserUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.*;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import org.apache.shiro.SecurityUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MerchantBaseService {

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    AreaService areaService;
    @Resource
    InvoiceConfigService invoiceConfigService;
    @Resource
    FollowUpRelationDomainService followUpRelationDomainService;
    @Resource
    MerchantQueryFacade merchantQueryFacade;
    @Resource
    private MerchantQueryRepository merchantQueryRepository;
    @Resource
    MerchantAccountFacade merchantAccountFacade;
    @Resource
    AdminDomainService adminDomainService;
    @Resource
    private ContactFacade contactFacade;
    @Resource
    private ContactRepository contactRepository;
    @Resource
    private FenceQueryFacade fenceQueryFacade;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;
    @Resource
    MerchantOuterRepository merchantOuterRepository;
    public void initQuery(MerchantQueryInput input) {
        Long bizUserId = AuthUserUtils.getBizUserId();
        if (bizUserId == null) {
            throw new BizException("LOGIN_FIRST");
        }
        if (input == null) {
            input = new MerchantQueryInput();
            input.setPageSize(10);
            input.setPageIndex(1);
        }

        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        //未登录则直接放行
        if (user == null) {
            return;
        }
        // 超管直接放行
        Set<Integer> roleIds = new HashSet<>(user.getRoleIds());
        for (Integer roleId : roleIds) {
            if (roleId == 1) {
                return;
            }
        }
        List<Integer> dataPermission = new ArrayList<>();
        String dataPermissionKey = String.format("auth-data-permission:%s", bizUserId);
        if (redisTemplate.hasKey(dataPermissionKey)) {
            String dataPermissionStr = (String) redisTemplate.opsForValue().get(dataPermissionKey);
            dataPermission = JSONObject.parseObject(dataPermissionStr, List.class);
            // 无权限直接提示
            if (CollectionUtils.isEmpty(dataPermission)) {
                throw new BizException("无数据授权");
            }
            // 0代表拥有所有数据权限
            if (dataPermission.contains(0)) {
                return;
            }
            input.setAreaNos(dataPermission);
        }
    }

    /**
     * 补充非
     */
    public void baseListMerge(List<MerchantVO> vos) {
        if (CollUtil.isEmpty(vos)) {
            return;
        }

        List<Long> mIds = vos.stream().map(MerchantVO::getMId).collect(Collectors.toList());
        List<Long> storeIds = vos.stream().map(MerchantVO::getStoreId).collect(Collectors.toList());
        List<Integer> areaNos = vos.stream().map(MerchantVO::getAreaNo).collect(Collectors.toList());
        List<Long> adminIds = vos.stream().map(MerchantVO::getAdminId).collect(Collectors.toList());

        //补充地区编码
        Map<Integer, Area> areaMap = areaService.getAreaMap(areaNos);

        //主账号信息
        Map<Long, List<MerchantStoreAccountResultResp>> accountMap = queryMerchantAccountMap(storeIds, MerchantAccountEnums.Type.MANAGER);

        //获取工商信息 发票抬头
        Map<Long, List<InvoiceConfig>> invoiceConfigTittleMap = invoiceConfigService.getInvoiceConfigTittleMap(mIds);

        //bd信息
        Map<Long, List<FollowUpRelationEntity>> followMap = followUpRelationDomainService.selectByMids(mIds);

        //大客户信息
        Map<Long, AdminEntity> adminMap = adminDomainService.getAdminMap(adminIds);

        Map<Long, MerchantEntity> merchantEntityMap = getMerchantEntityMap(mIds);
        vos.forEach(
                it -> {
                    if (it.getAreaNo() != null) {
                        Area area = areaMap.get(it.getAreaNo());
                        if (area != null) {
                            it.setAreaName(area.getAreaName());
                        }
                    }
                    if (it.getAdminId() != null) {
                        AdminEntity adminEntity = adminMap.get(it.getAdminId());
                        it.setAdminEntity(adminEntity);
                        if (adminEntity != null) {
                            it.setRealName(adminEntity.getRealname() + adminEntity.getNameRemakes());
                            it.setContractMethod(adminEntity.getContractMethod());
                        }
                    }
                    it.setPopMerchant(isPopMerchant(it.getBusinessLine()));
                    Long mId = it.getMId();
                    MerchantEntity entity = merchantEntityMap.get(mId);
                    if (entity != null) {
                        it.setDirect(entity.getDirect());
                        it.setGrade(entity.getGrade());
                    }
                    List<InvoiceConfig> invoiceConfigs = invoiceConfigTittleMap.get(mId);
                    if (!CollectionUtils.isEmpty(invoiceConfigs)) {
                        it.setCompanyBrand(invoiceConfigs.get(0).getInvoiceTitle());
                    }
                    List<FollowUpRelationEntity> followUpRelationEntities = followMap.get(mId);
                    if (!CollectionUtils.isEmpty(followUpRelationEntities)) {
                        String adminName = followUpRelationEntities.get(0).getReassign() ? "默认邀请码" : followUpRelationEntities.get(0).getAdminName();
                        it.setAdminRealName(adminName);
                    }
                    List<MerchantStoreAccountResultResp> merchantStoreAccountResultResps = accountMap.get(it.getStoreId());
                    if (!CollectionUtils.isEmpty(merchantStoreAccountResultResps)) {
                        MerchantStoreAccountResultResp merchantStoreAccountResultResp = merchantStoreAccountResultResps.get(0);
                        it.setPhone(merchantStoreAccountResultResp.getPhone());
                        it.setMcontact(merchantStoreAccountResultResp.getAccountName());
                    }
                }
        );

    }

    private boolean isPopMerchant(Integer businessLine) {
        return MerchantEnums.BusinessLineEnum.POP.getCode().equals(businessLine);
    }

    public Map<Long/*adminId*/, MerchantEntity> getMerchantEntityMap(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Maps.newHashMap();
        }
        mids = mids.stream().distinct().collect(Collectors.toList());
        List<MerchantEntity> merchantEntities = merchantQueryRepository.selectByIds(mids);
        return merchantEntities.stream().collect(Collectors.toMap(MerchantEntity::getMId, Function.identity(), (x1, x2) -> x1));
    }

    /**
     * 根据store_id获取当前门店下的收货地址
     *
     * @return
     */
    public List<ContactVO> queryContactMap(Long storeId, Long mId) {
        if (null == storeId) {
            return new ArrayList<>();
        }
        List<ContactVO> contactVoList = queryNormalContact(Collections.singletonList(storeId));
        if (CollUtil.isEmpty(contactVoList)) {
            return new ArrayList<>();
        }
        MerchantEntity merchantEntity = merchantQueryRepository.selectById(mId);
        if (null == merchantEntity) {
            return new ArrayList<>();
        }

        // 补充鲜沐数据
        List<ContactFenceQueryInput> inputs = new ArrayList<>(contactVoList.size());
        for (ContactVO contactVO : contactVoList) {
            try {
                contactVO.setMId(mId);
                ContactEntity contact = contactRepository.selectByPrimaryKey(contactVO.getContactId());
                contactVO.setPath(contact.getPath());
                contactVO.setAcmId(contact.getAcmId());
                contactVO.setStoreNo(contact.getStoreNo());
                contactVO.setDeliveryFrequent(contact.getDeliveryFrequent());
                contactVO.setDeliveryRule(contact.getDeliveryRule());
                contactVO.setDeliveryFee(contact.getDeliveryFee());
                AreaQueryResp area = fenceQueryFacade.getArea(contactVO.getCity(), contactVO.getArea(), contactVO.getPoiNote());
                if (null != area) {
                    contactVO.setAreaName(area.getAreaName());
                    contactVO.setAreaNo(area.getAreaNo());
                }
            } catch (Exception e) {
                log.warn("获取地址所属区域异常", e);
            }
            DistributionRulesInfoInput input = new DistributionRulesInfoInput();
            input.setTypeId(contactVO.getContactId());
            input.setType(DistributionRulesTypeEnum.MERCHANT.getCode());
            DistributionRulesDTO info = contactFacade.getInfo(input);
            contactVO.setDistributionRulesDTO(info);

            //组装查询围栏状态数据
            ContactFenceQueryInput contactFenceQueryInput = new ContactFenceQueryInput();
            contactFenceQueryInput.setContactId(contactVO.getContactId());
            contactFenceQueryInput.setAddress(contactVO.getAddress());
            contactFenceQueryInput.setArea(contactVO.getArea());
            contactFenceQueryInput.setCity(contactVO.getCity());
            contactFenceQueryInput.setProvince(contactVO.getProvince());
            contactFenceQueryInput.setPoiNote(contactVO.getPoiNote());
            inputs.add(contactFenceQueryInput);
        }

        //查询围栏状态
        ContactBelongFenceReq contactBelongFenceReq = new ContactBelongFenceReq();
        contactBelongFenceReq.setContacts(inputs);
        contactBelongFenceReq.setFenceChannelType(FenceChannelTypeEnum.getChannelCode(merchantEntity.getSize()));
        Map<Long, ContactBelongFenceDTO> fenceDTOMap = contactFacade.batchQueryContactAddressBelongFence(contactBelongFenceReq);
        if (!CollectionUtils.isEmpty(fenceDTOMap)) {
            contactVoList.stream().forEach(contactVO -> {
                ContactBelongFenceDTO belongFenceDTO = fenceDTOMap.get(contactVO.getContactId());
                if (Objects.nonNull(belongFenceDTO)) {
                    contactVO.setFenceStatus(belongFenceDTO.getStatus());
                } else {
                    contactVO.setFenceStatus(FenceStatusEnum.OPEN.getCode());
                }
            });
        } else {
            //没有返回就默认为暂停状态
            contactVoList.stream().forEach(contact -> contact.setFenceStatus(FenceStatusEnum.OPEN.getCode()));
        }
        return contactVoList;
    }

    private void contactListMerge(MerchantVO vo) {
        if (null == vo) {
            return;
        }
        List<ContactVO> contactStoreIdMap = queryContactMap(vo.getStoreId(), vo.getMId());
        vo.setContacts(contactStoreIdMap);
    }

    /**
     * 补充列表信息和联系人信息
     */
    public void baseDetailMerge(MerchantVO vo) {
        List<MerchantVO> vos = Collections.singletonList(vo);

        //基础信息
        baseListMerge(vos);

        // 联系人-地址
        contactListMerge(vo);

        // 一些商城自己域的东西
        mergeMerchantEntity(vo);

        List<MerchantOuterEntity> merchantOuterEntities = merchantOuterRepository.selectByMid(Arrays.asList(vo.getMId()));
        vo.setOuterMappings(merchantOuterEntities);

        // tms配置
        String result = merchantQueryFacade.queryMerchantPropertiesExtValue(vo.getMId(), MerchantPropertiesExtEnum.TMS_PRINT_OUT_CONFIG.name());
        if (StringUtils.isEmpty(result)){
            vo.setPrintOutTMSConfig(true);
        }else if (Objects.equals(result, String.valueOf(MerchantPropertiesExtEnum.TMS_PRINT_OUT_CONFIG.getId()))){
            vo.setPrintOutTMSConfig(true);
        }else {
            vo.setPrintOutTMSConfig(false);
        }
    }

    public void mergeMerchantEntity(MerchantVO vo) {
        // 查询业务属性
        MerchantEntity entity = merchantQueryRepository.selectById(vo.getMId());

        //补充业务属性
        mergeEntity(entity, vo);

        // 查询推荐码信息
        MerchantStoreResultResp sourceMerchant = merchantQueryFacade.getMerchantSource(vo.getStoreId());
        if (sourceMerchant != null) {
            vo.setInviterMerchantName(sourceMerchant.getStoreName());
        }
        //拉黑备注信息
        MerchantStoreChangeLogResultResp merchantBackMsg = merchantQueryFacade.getMerchantBackMsg(vo.getStoreId());
        if (merchantBackMsg != null) {
            vo.setPullBlackRemark(merchantBackMsg.getOpRemark());
            vo.setPullBlackOperator(merchantBackMsg.getOpName());
        }
    }

    private void mergeEntity(MerchantEntity entity, MerchantVO vo) {
        //补充地址
        if (entity == null || vo == null) {
            return;
        }
        vo.setAddress(entity.getAddress());
        vo.setSkuShow(entity.getSkuShow());
        vo.setLoginTime(entity.getLoginTime());
        vo.setServer(entity.getServer());
        vo.setChangePop(entity.getChangePop());
        vo.setDoorPic(entity.getDoorPic());
        vo.setLastOrderTime(entity.getLastOrderTime());
        vo.setShopSign(entity.getShopSign());
        vo.setHouseNumber(entity.getHouseNumber());
        vo.setRemark(entity.getRemark());
        vo.setShowPrice(entity.getShowPrice());
        vo.setDirect(entity.getDirect());
        vo.setExamineType(entity.getExamineType());
        vo.setMainBusinessType(entity.getMainBusinessType());
        vo.setSideBusinessType(entity.getSideBusinessType());
        vo.setMerchantChainType(entity.getMerchantChainType());
    }


    public PageInfo<MerchantVO> getBasePage(MerchantQueryInput input) {
        MerchantStorePageQueryReq merchantStorePageQueryReq = MerchantPageQueryConverter.toMerchantStorePageQueryReq(input);
        PageInfo<XmMerchantStorePageResp> xmMerchantStorePageRespPageInfo = merchantQueryFacade.pageQuery(merchantStorePageQueryReq);
        return PageInfoConverter.toPageResp(xmMerchantStorePageRespPageInfo, XmMerchantStorePageRespConvert::toMerchantVO);
    }





    public List<MerchantVO> getMerchantStoreAndExtends(MerchantQueryInput input) {
        MerchantStoreQueryReq merchantStoreQueryReq = MerchantQueryConverter.toMerchantStoreQueryReq(input);
        merchantStoreQueryReq.setQueryManageAccount(true);
        List<MerchantStoreAndExtendResp> merchantStoreAndExtends = merchantQueryFacade.getMerchantStoreAndExtends(merchantStoreQueryReq);
        return XmMerchantStoreRespConvert.toMerchantVOList(merchantStoreAndExtends);
    }

    public Map<Long, List<MerchantStoreAccountResultResp>> queryMerchantAccountMap(List<Long> storeIds, MerchantAccountEnums.Type type) {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setStoreIdList(storeIds);
        req.setType(type.getCode());
        req.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
        List<MerchantStoreAccountResultResp> merchantStoreAccountResultResps = merchantAccountFacade.queryMerchantAccount(req).stream().filter(it->it.getStoreId()!=null).collect(Collectors.toList());
        return merchantStoreAccountResultResps.stream().collect(Collectors.groupingBy(MerchantStoreAccountResultResp::getStoreId));
    }

    public List<ContactVO> queryNormalContact(List<Long> storeIds) {
        return this.converterToContactVOList(contactFacade.queryNormalContact(storeIds));
    }

    private List<ContactVO> converterToContactVOList(List<MerchantAddressDomainResp> addressDomainResps) {
        if (CollUtil.isEmpty(addressDomainResps)) {
            return Collections.emptyList();
        }
        List<ContactVO> contactEntities = new ArrayList<>();
        addressDomainResps.forEach(address -> {
            ContactVO vo = new ContactVO();
            vo.setTenantId(address.getTenantId());
            vo.setStoreId(address.getStoreId());
            vo.setProvince(address.getProvince());
            vo.setCity(address.getCity());
            vo.setArea(address.getArea());
            vo.setAddress(address.getAddress());
            vo.setHouseNumber(address.getHouseNumber());
            vo.setPoiNote(address.getPoiNote());
            vo.setIsDefault(address.getDefaultFlag());
            vo.setStatus(address.getStatus());
            vo.setContactId(address.getXmContactId());
            vo.setRemark(address.getRemark());
            vo.setAddressRemark(address.getAddressRemark());
            vo.setContactAddressRemark(JSONObject.parseObject(address.getAddressRemark(), MerchantContactAddressRemarkVO.class));
            List<MerchantContactResultResp> list = address.getContactList();
            if (CollUtil.isNotEmpty(list)) {
                MerchantContactResultResp merchantContactResultResp = list.get(0);
                vo.setPhone(merchantContactResultResp.getPhone());
                vo.setContact(merchantContactResultResp.getName());
            }
            contactEntities.add(vo);
        });
        return contactEntities;
    }


}
