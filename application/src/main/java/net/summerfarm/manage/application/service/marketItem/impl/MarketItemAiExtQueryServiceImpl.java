package net.summerfarm.manage.application.service.marketItem.impl;


import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtQueryService;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.query.MarketItemAiExtQueryInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Service
public class MarketItemAiExtQueryServiceImpl implements MarketItemAiExtQueryService {

    @Autowired
    private MarketItemAiExtQueryRepository marketItemAiExtQueryRepository;

    @Override
    public PageInfo<MarketItemAiExtEntity> getPage(MarketItemAiExtQueryInput input) {
        MarketItemAiExtQueryParam queryParam = MarketItemAiExtAssembler.toMarketItemAiExtQueryParam(input);
        return marketItemAiExtQueryRepository.getPage(queryParam);
    }

    @Override
    public MarketItemAiExtEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return marketItemAiExtQueryRepository.selectById(id);
    }
}