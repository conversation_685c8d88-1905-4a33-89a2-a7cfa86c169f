package net.summerfarm.manage.application.service.inventory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.enums.DistOrderSourceEnum;
import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.ContactRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.facade.inventory.SaleInventoryCenterQueryFacade;
import net.summerfarm.manage.facade.inventory.dto.AreaStoreQueryDTO;
import net.summerfarm.manage.facade.inventory.dto.AreaStoreQueryRes;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售库存查询服务
 * <AUTHOR>
 * @Date 2025/7/16 13:54
 * @Version 1.0
 */
@Slf4j
@Component
public class SaleInventoryQueryService {

    @Autowired
    private SaleInventoryCenterQueryFacade saleInventoryCenterQueryFacade;

    @Autowired
    private ContactRepository contactRepository;

    @Autowired
    private MerchantQueryRepository merchantQueryRepository;

    /**
     * 查询销售库存
     * @param mId
     * @param skuList
     * @return
     */
    public Map<String, AreaStoreQueryRes> querySaleInventoryByMId(Long mId, List<String> skuList) {
        if (null == mId || CollectionUtils.isEmpty(skuList)) {
            return new HashMap<>();
        }
        final MerchantEntity merchantEntity = merchantQueryRepository.selectById(mId);
        if (null == merchantEntity){
            throw new BizException("门店信息不存在");
        }
        ContactEntity contact = contactRepository.selectDefaultContactByMid(mId);
        if (null == contact){
            throw new BizException("门店的地址信息不存在");
        }
        final AreaStoreQueryDTO areaStoreQueryDTO = new AreaStoreQueryDTO();
        areaStoreQueryDTO.setSkuCodeList(skuList);
        areaStoreQueryDTO.setMId(mId);
        areaStoreQueryDTO.setSource(DistOrderSourceEnum.getDistOrderSource(merchantEntity.getBusinessLine()));
//        areaStoreQueryDTO.setAddOrderFlag();
        areaStoreQueryDTO.setContactId(contact.getContactId());
        areaStoreQueryDTO.setProvince(contact.getProvince());
        areaStoreQueryDTO.setCity(contact.getCity());
        areaStoreQueryDTO.setArea(contact.getArea());
        areaStoreQueryDTO.setAddress(contact.getAddress());
        areaStoreQueryDTO.setStoreNo(contact.getStoreNo());
        areaStoreQueryDTO.setPoiNote(contact.getPoiNote());
        return saleInventoryCenterQueryFacade.getSaleInventoryInfo(areaStoreQueryDTO);
    }
}
