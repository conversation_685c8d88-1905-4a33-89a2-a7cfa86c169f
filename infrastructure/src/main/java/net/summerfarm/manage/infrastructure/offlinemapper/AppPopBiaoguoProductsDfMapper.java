package net.summerfarm.manage.infrastructure.offlinemapper;

import net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoProductsDf;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-12 11:19:19
 * @version 1.0
 *
 */
@Mapper
public interface AppPopBiaoguoProductsDfMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(AppPopBiaoguoProductsDf record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(AppPopBiaoguoProductsDf record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AppPopBiaoguoProductsDf selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<AppPopBiaoguoProductsDf> selectByCondition(AppPopBiaoguoProductsDfQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<AppPopBiaoguoProductsDfEntity> getPage(AppPopBiaoguoProductsDfQueryParam param);

    /**
     * 指定条件是否存在数据
     *
     * <AUTHOR>
     * @date 2024/12/18 15:05
     */
    int exist(AppPopBiaoguoProductsDfQueryParam productsDfQueryParam);

    /**
     * 指定条件数量
     *
     * <AUTHOR>
     * @date 2024/12/24 11:13
     */
    int count(AppPopBiaoguoProductsDfQueryParam productsDfQueryParam);
}

