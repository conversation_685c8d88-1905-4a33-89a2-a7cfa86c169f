package net.summerfarm.manage.infrastructure.repository.searchSynonym;


import net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary;
import net.summerfarm.manage.infrastructure.mapper.searchSynonym.ProductSearchSynonymDictionaryMapper;
import net.summerfarm.manage.infrastructure.converter.searchSynonym.ProductSearchSynonymDictionaryConverter;
import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryQueryRepository;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-04-24 14:53:58
* @version 1.0
*
*/
@Repository
public class ProductSearchSynonymDictionaryQueryRepositoryImpl implements ProductSearchSynonymDictionaryQueryRepository {

    @Autowired
    private ProductSearchSynonymDictionaryMapper productSearchSynonymDictionaryMapper;


    @Override
    public PageInfo<ProductSearchSynonymDictionaryEntity> getPage(ProductSearchSynonymDictionaryQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ProductSearchSynonymDictionaryEntity> entities = productSearchSynonymDictionaryMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ProductSearchSynonymDictionaryEntity selectById(Long id) {
        return ProductSearchSynonymDictionaryConverter.toProductSearchSynonymDictionaryEntity(productSearchSynonymDictionaryMapper.selectById(id));
    }


    @Override
    public List<ProductSearchSynonymDictionaryEntity> selectByCondition(ProductSearchSynonymDictionaryQueryParam param) {
        return ProductSearchSynonymDictionaryConverter.toProductSearchSynonymDictionaryEntityList(productSearchSynonymDictionaryMapper.selectByCondition(param));
    }

}