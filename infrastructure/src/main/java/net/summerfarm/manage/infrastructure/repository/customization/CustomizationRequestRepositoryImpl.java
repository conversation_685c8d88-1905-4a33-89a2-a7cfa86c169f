package net.summerfarm.manage.infrastructure.repository.customization;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestEntity;
import net.summerfarm.manage.domain.customization.param.CustomizationRequestQueryParam;
import net.summerfarm.manage.domain.customization.repository.CustomizationRequestRepository;
import net.summerfarm.manage.infrastructure.converter.CustomizationRequestConverter;
import net.summerfarm.manage.infrastructure.mapper.customization.CustomizationRequestMapper;
import net.summerfarm.manage.infrastructure.model.customization.CustomizationRequest;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 定制需求仓储实现
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Repository
public class CustomizationRequestRepositoryImpl implements CustomizationRequestRepository {

    @Autowired
    private CustomizationRequestMapper customizationRequestMapper;

    @Override
    public CustomizationRequestEntity save(CustomizationRequestEntity entity) {
        CustomizationRequest model = CustomizationRequestConverter.entityToModel(entity);
        customizationRequestMapper.insert(model);
        entity.setId(model.getId());
        return entity;
    }

    @Override
    public CustomizationRequestEntity update(CustomizationRequestEntity entity) {
        CustomizationRequest model = CustomizationRequestConverter.entityToModel(entity);
        customizationRequestMapper.updateById(model);
        return entity;
    }

    @Override
    public void updateStatusByMasterOrderNo(Integer byOrdertatus, String orderNo) {
        customizationRequestMapper.updateStatusByMasterOrderNo(byOrdertatus,orderNo);
    }

    @Override
    public CustomizationRequestEntity findById(Long id) {
        CustomizationRequest model = customizationRequestMapper.selectById(id);
        return model != null ? CustomizationRequestConverter.modelToEntity(model) : null;
    }


    @Override
    public List<CustomizationRequestEntity> findList(CustomizationRequestQueryParam input) {
        List<CustomizationRequest> models = customizationRequestMapper.selectList(input);
        return models.stream()
                .map(CustomizationRequestConverter::modelToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public PageInfo<CustomizationRequestEntity> page(CustomizationRequestQueryParam param, Integer pageIndex, Integer pageSize) {
        if (Objects.isNull(pageIndex) || Objects.isNull(pageSize)) {
            return new PageInfo<>(Lists.newArrayList());
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<CustomizationRequest> models = customizationRequestMapper.selectList(param);

        // 先创建PageInfo保留分页信息
        PageInfo<CustomizationRequest> pageInfo = new PageInfo<>(models);

        // 转换实体
        List<CustomizationRequestEntity> entities = models.stream()
                .map(CustomizationRequestConverter::modelToEntity)
                .collect(Collectors.toList());

        // 使用PageInfo的静态方法创建新的PageInfo，保持分页信息
        PageInfo<CustomizationRequestEntity> result = PageInfo.of(entities);
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());

        return result;
    }

    @Override
    public CustomizationRequestEntity queryByMasterOrderNo(String masterOrderNo) {
        CustomizationRequest model = customizationRequestMapper.queryByMasterOrderNo(masterOrderNo);
        return CustomizationRequestConverter.modelToEntity(model);
    }

}
