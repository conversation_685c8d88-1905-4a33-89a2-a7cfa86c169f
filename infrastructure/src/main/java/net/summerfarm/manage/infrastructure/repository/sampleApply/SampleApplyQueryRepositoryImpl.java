package net.summerfarm.manage.infrastructure.repository.sampleApply;


import net.summerfarm.manage.domain.sampleApply.flatObject.SampleOrderFlatObject;
import net.summerfarm.manage.infrastructure.mapper.sampleApply.SampleApplyMapper;
import net.summerfarm.manage.infrastructure.converter.sampleApply.SampleApplyConverter;
import net.summerfarm.manage.domain.sampleApply.repository.SampleApplyQueryRepository;
import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import net.summerfarm.manage.domain.sampleApply.param.query.SampleApplyQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-01-02 14:00:39
* @version 1.0
*
*/
@Repository
public class SampleApplyQueryRepositoryImpl implements SampleApplyQueryRepository {

    @Autowired
    private SampleApplyMapper sampleApplyMapper;


    @Override
    public PageInfo<SampleApplyEntity> getPage(SampleApplyQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<SampleApplyEntity> entities = sampleApplyMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public SampleApplyEntity selectById(Long sampleId) {
        return SampleApplyConverter.toSampleApplyEntity(sampleApplyMapper.selectById(sampleId));
    }


    @Override
    public List<SampleApplyEntity> selectByCondition(SampleApplyQueryParam param) {
        return SampleApplyConverter.toSampleApplyEntityList(sampleApplyMapper.selectByCondition(param));
    }

    @Override
    public List<SampleOrderFlatObject> queryValidSampleOrderDeliveryDetail(List<String> sampleIdList) {
        if(CollectionUtils.isEmpty(sampleIdList)){
            return Collections.emptyList();
        }

        return sampleApplyMapper.queryValidSampleOrderDeliveryDetail(sampleIdList);
    }
}