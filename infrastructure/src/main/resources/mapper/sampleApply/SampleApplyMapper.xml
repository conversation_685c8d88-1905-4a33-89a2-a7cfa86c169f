<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.sampleApply.SampleApplyMapper">
    <!-- 结果集映射 -->
    <resultMap id="sampleApplyResultMap" type="net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply">
		<id column="sample_id" property="sampleId" jdbcType="INTEGER"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="create_id" property="createId" jdbcType="INTEGER"/>
		<result column="create_name" property="createName" jdbcType="VARCHAR"/>
		<result column="m_id" property="mId" jdbcType="INTEGER"/>
		<result column="m_name" property="mName" jdbcType="VARCHAR"/>
		<result column="grade" property="grade" jdbcType="INTEGER"/>
		<result column="m_size" property="mSize" jdbcType="VARCHAR"/>
		<result column="m_phone" property="mPhone" jdbcType="VARCHAR"/>
		<result column="m_contact" property="mContact" jdbcType="VARCHAR"/>
		<result column="contact_id" property="contactId" jdbcType="INTEGER"/>
		<result column="bd_id" property="bdId" jdbcType="INTEGER"/>
		<result column="bd_name" property="bdName" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="satisfaction" property="satisfaction" jdbcType="INTEGER"/>
		<result column="purchase_intention" property="purchaseIntention" jdbcType="INTEGER"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
		<result column="store_no" property="storeNo" jdbcType="INTEGER"/>
		<result column="risk_level" property="riskLevel" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="sampleApplyColumns">
          t.sample_id,
          t.add_time,
          t.update_time,
          t.create_id,
          t.create_name,
          t.m_id,
          t.m_name,
          t.grade,
          t.m_size,
          t.m_phone,
          t.m_contact,
          t.contact_id,
          t.bd_id,
          t.bd_name,
          t.status,
          t.satisfaction,
          t.purchase_intention,
          t.remark,
          t.area_no,
          t.delivery_time,
          t.store_no,
          t.risk_level
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="sampleId != null">
                AND t.sample_id = #{sampleId}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="createId != null">
                AND t.create_id = #{createId}
            </if>
			<if test="createName != null and createName !=''">
                AND t.create_name = #{createName}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="mName != null and mName !=''">
                AND t.m_name = #{mName}
            </if>
			<if test="grade != null">
                AND t.grade = #{grade}
            </if>
			<if test="mSize != null and mSize !=''">
                AND t.m_size = #{mSize}
            </if>
			<if test="mPhone != null and mPhone !=''">
                AND t.m_phone = #{mPhone}
            </if>
			<if test="mContact != null and mContact !=''">
                AND t.m_contact = #{mContact}
            </if>
			<if test="contactId != null">
                AND t.contact_id = #{contactId}
            </if>
			<if test="bdId != null">
                AND t.bd_id = #{bdId}
            </if>
			<if test="bdName != null and bdName !=''">
                AND t.bd_name = #{bdName}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="satisfaction != null">
                AND t.satisfaction = #{satisfaction}
            </if>
			<if test="purchaseIntention != null">
                AND t.purchase_intention = #{purchaseIntention}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
			<if test="deliveryTime != null">
                AND t.delivery_time = #{deliveryTime}
            </if>
			<if test="storeNo != null">
                AND t.store_no = #{storeNo}
            </if>
			<if test="riskLevel != null">
                AND t.risk_level = #{riskLevel}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="createId != null">
                    t.create_id = #{createId},
                </if>
                <if test="createName != null">
                    t.create_name = #{createName},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="mName != null">
                    t.m_name = #{mName},
                </if>
                <if test="grade != null">
                    t.grade = #{grade},
                </if>
                <if test="mSize != null">
                    t.m_size = #{mSize},
                </if>
                <if test="mPhone != null">
                    t.m_phone = #{mPhone},
                </if>
                <if test="mContact != null">
                    t.m_contact = #{mContact},
                </if>
                <if test="contactId != null">
                    t.contact_id = #{contactId},
                </if>
                <if test="bdId != null">
                    t.bd_id = #{bdId},
                </if>
                <if test="bdName != null">
                    t.bd_name = #{bdName},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="satisfaction != null">
                    t.satisfaction = #{satisfaction},
                </if>
                <if test="purchaseIntention != null">
                    t.purchase_intention = #{purchaseIntention},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="deliveryTime != null">
                    t.delivery_time = #{deliveryTime},
                </if>
                <if test="storeNo != null">
                    t.store_no = #{storeNo},
                </if>
                <if test="riskLevel != null">
                    t.risk_level = #{riskLevel},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="sampleApplyResultMap" >
        SELECT <include refid="sampleApplyColumns" />
        FROM sample_apply t
		WHERE t.sample_id = #{sampleId}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.sampleApply.param.query.SampleApplyQueryParam"  resultType="net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity" >
        SELECT
            t.sample_id sampleId,
            t.add_time addTime,
            t.update_time updateTime,
            t.create_id createId,
            t.create_name createName,
            t.m_id mId,
            t.m_name mName,
            t.grade grade,
            t.m_size mSize,
            t.m_phone mPhone,
            t.m_contact mContact,
            t.contact_id contactId,
            t.bd_id bdId,
            t.bd_name bdName,
            t.status status,
            t.satisfaction satisfaction,
            t.purchase_intention purchaseIntention,
            t.remark remark,
            t.area_no areaNo,
            t.delivery_time deliveryTime,
            t.store_no storeNo,
            t.risk_level riskLevel
        FROM sample_apply t
        <include refid="whereColumnBySelect" />
            ORDER BY t.sample_id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.sampleApply.param.query.SampleApplyQueryParam" resultMap="sampleApplyResultMap" >
        SELECT <include refid="sampleApplyColumns" />
        FROM sample_apply t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply" keyProperty="sampleId" useGeneratedKeys="true">
        INSERT INTO sample_apply
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="sampleId != null">
				  sample_id,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="createId != null">
				  create_id,
              </if>
              <if test="createName != null">
				  create_name,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="mName != null">
				  m_name,
              </if>
              <if test="grade != null">
				  grade,
              </if>
              <if test="mSize != null">
				  m_size,
              </if>
              <if test="mPhone != null">
				  m_phone,
              </if>
              <if test="mContact != null">
				  m_contact,
              </if>
              <if test="contactId != null">
				  contact_id,
              </if>
              <if test="bdId != null">
				  bd_id,
              </if>
              <if test="bdName != null">
				  bd_name,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="satisfaction != null">
				  satisfaction,
              </if>
              <if test="purchaseIntention != null">
				  purchase_intention,
              </if>
              <if test="remark != null">
				  remark,
              </if>
              <if test="areaNo != null">
				  area_no,
              </if>
              <if test="deliveryTime != null">
				  delivery_time,
              </if>
              <if test="storeNo != null">
				  store_no,
              </if>
              <if test="riskLevel != null">
				  risk_level,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="sampleId != null">
				#{sampleId,jdbcType=INTEGER},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="createId != null">
				#{createId,jdbcType=INTEGER},
              </if>
              <if test="createName != null">
				#{createName,jdbcType=VARCHAR},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=INTEGER},
              </if>
              <if test="mName != null">
				#{mName,jdbcType=VARCHAR},
              </if>
              <if test="grade != null">
				#{grade,jdbcType=INTEGER},
              </if>
              <if test="mSize != null">
				#{mSize,jdbcType=VARCHAR},
              </if>
              <if test="mPhone != null">
				#{mPhone,jdbcType=VARCHAR},
              </if>
              <if test="mContact != null">
				#{mContact,jdbcType=VARCHAR},
              </if>
              <if test="contactId != null">
				#{contactId,jdbcType=INTEGER},
              </if>
              <if test="bdId != null">
				#{bdId,jdbcType=INTEGER},
              </if>
              <if test="bdName != null">
				#{bdName,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="satisfaction != null">
				#{satisfaction,jdbcType=INTEGER},
              </if>
              <if test="purchaseIntention != null">
				#{purchaseIntention,jdbcType=INTEGER},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
              <if test="areaNo != null">
				#{areaNo,jdbcType=INTEGER},
              </if>
              <if test="deliveryTime != null">
				#{deliveryTime,jdbcType=DATE},
              </if>
              <if test="storeNo != null">
				#{storeNo,jdbcType=INTEGER},
              </if>
              <if test="riskLevel != null">
				#{riskLevel,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply" >
        UPDATE sample_apply t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.sample_id = #{sampleId,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply" >
        DELETE FROM sample_apply
		WHERE sample_id = #{sampleId,jdbcType=INTEGER}
    </delete>

    <select id="queryValidSampleOrderDeliveryDetail" resultType="net.summerfarm.manage.domain.sampleApply.flatObject.SampleOrderFlatObject">
        select sa.sample_id                                                                        sampleId,
               m.mname                                                                             mname,
               m.size                                                                              msize,
               m.m_id                                                                              mId,
               con.contact                                                                         contactName,
               sa.delivery_time
                                                                                                   deliveryTime,
               con.phone                                                                           contactPhone,
               CONCAT(con.province, con.city, con.area, con.address, ifnull(con.house_number, '')) contactAddress,
               ss.pd_name                                                                          pdName,
               ss.amount                                                                           amount,
               ss.weight                                                                           weight,
               ss.sku                                                                              sku,
               con.contact_id                                                                      contactId,
               sa.store_no                                                                         storeNo,
               a.name_remakes                                                                      brandName,
               a.admin_id                                                                          bigCustomerId
        from sample_apply sa
                 left join sample_sku ss on ss.sample_id = sa.sample_id
                 left join merchant m on m.m_id = sa.m_id
                 left join admin a on m.admin_id=a.admin_id
                 left join contact con on sa.contact_id = con.contact_id
        where sa.status in(0,1) and
          sa.sample_id in
          <foreach collection="sampleIdList" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
          </foreach>
    </select>

</mapper>