<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.afterSale.AfterSaleDeliveryPathMapper">
    <!-- 结果集映射 -->
    <resultMap id="afterSaleDeliveryPathResultMap" type="net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
		<result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="INTEGER"/>
		<result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
		<result column="concat_id" property="concatId" jdbcType="NUMERIC"/>
		<result column="after_sale_no" property="afterSaleNo" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="out_store_no" property="outStoreNo" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="afterSaleDeliveryPathColumns">
          t.id,
          t.gmt_create,
          t.gmt_modified,
          t.m_id,
          t.delivery_time,
          t.concat_id,
          t.after_sale_no,
          t.type,
          t.out_store_no,
          t.status
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="gmtCreate != null">
                AND t.gmt_create = #{gmtCreate}
            </if>
			<if test="gmtModified != null">
                AND t.gmt_modified = #{gmtModified}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="deliveryTime != null">
                AND t.delivery_time = #{deliveryTime}
            </if>
			<if test="concatId != null">
                AND t.concat_id = #{concatId}
            </if>
			<if test="afterSaleNo != null and afterSaleNo !=''">
                AND t.after_sale_no = #{afterSaleNo}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="outStoreNo != null">
                AND t.out_store_no = #{outStoreNo}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="gmtCreate != null">
                    t.gmt_create = #{gmtCreate},
                </if>
                <if test="gmtModified != null">
                    t.gmt_modified = #{gmtModified},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="deliveryTime != null">
                    t.delivery_time = #{deliveryTime},
                </if>
                <if test="concatId != null">
                    t.concat_id = #{concatId},
                </if>
                <if test="afterSaleNo != null">
                    t.after_sale_no = #{afterSaleNo},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="outStoreNo != null">
                    t.out_store_no = #{outStoreNo},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="afterSaleDeliveryPathResultMap" >
        SELECT <include refid="afterSaleDeliveryPathColumns" />
        FROM after_sale_delivery_path t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.afterSale.param.query.AfterSaleDeliveryPathQueryParam"  resultType="net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity" >
        SELECT
            t.id id,
            t.gmt_create gmtCreate,
            t.gmt_modified gmtModified,
            t.m_id mId,
            t.delivery_time deliveryTime,
            t.concat_id concatId,
            t.after_sale_no afterSaleNo,
            t.type type,
            t.out_store_no outStoreNo,
            t.status status
        FROM after_sale_delivery_path t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.afterSale.param.query.AfterSaleDeliveryPathQueryParam" resultMap="afterSaleDeliveryPathResultMap" >
        SELECT <include refid="afterSaleDeliveryPathColumns" />
        FROM after_sale_delivery_path t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO after_sale_delivery_path
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="gmtCreate != null">
				  gmt_create,
              </if>
              <if test="gmtModified != null">
				  gmt_modified,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="deliveryTime != null">
				  delivery_time,
              </if>
              <if test="concatId != null">
				  concat_id,
              </if>
              <if test="afterSaleNo != null">
				  after_sale_no,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="outStoreNo != null">
				  out_store_no,
              </if>
              <if test="status != null">
				  status,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="gmtCreate != null">
				#{gmtCreate,jdbcType=TIMESTAMP},
              </if>
              <if test="gmtModified != null">
				#{gmtModified,jdbcType=TIMESTAMP},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=INTEGER},
              </if>
              <if test="deliveryTime != null">
				#{deliveryTime,jdbcType=DATE},
              </if>
              <if test="concatId != null">
				#{concatId,jdbcType=NUMERIC},
              </if>
              <if test="afterSaleNo != null">
				#{afterSaleNo,jdbcType=VARCHAR},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="outStoreNo != null">
				#{outStoreNo,jdbcType=INTEGER},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath" >
        UPDATE after_sale_delivery_path t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath" >
        DELETE FROM after_sale_delivery_path
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <select id="queryValidAfterSaleDeliveryPathDetail" resultType="net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject">
        select asdp.id                                                                             as id,
               asdp.after_sale_no                                                                  as afterSaleNo,
               asdp.out_store_no                                                                   as storeNo,
               m.mname                                                                             as mname,
               m.size                                                                              as msize,
               m.m_id                                                                              as mId,
               con.contact                                                                         as contactName,
               asdp.delivery_time                                                                  as deliveryTime,
               asdp.type                                                                           as deliveryType,
               asdd.type                                                                           as orderItemDeliveryType,
               con.phone                                                                           as contactPhone,
               CONCAT(con.province, con.city, con.area, con.address, ifnull(con.house_number, '')) as contactAddress,
               asdd.pd_name                                                                        as pdName,
               asdd.quantity                                                                       as quantity,
               asdd.weight                                                                         as weight,
               asdd.sku                                                                            as sku,
               con.contact_id                                                                      as contactId,
               a.name_remakes                                                                      as brandName,
               a.admin_id                                                                          as bigCustomerId

        from after_sale_delivery_path asdp
                 left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id and asdd.status = 1
                 left join merchant m on m.m_id = asdp.m_id
                 left join admin a on m.admin_id=a.admin_id
                 left join contact con on asdp.concat_id = con.contact_id
        where asdp.status != 0 and asdp.after_sale_no in
          <foreach collection="afterSaleNoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
    </select>


</mapper>