<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.timing.TimingRuleMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.timing.TimingRule">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="timing_sku" property="timingSku" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="display" property="display" jdbcType="TINYINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="delivery_start" property="deliveryStart" jdbcType="DATE"/>
        <result column="delivery_end" property="deliveryEnd" jdbcType="DATE"/>
        <result column="rule_information" property="ruleInformation" jdbcType="VARCHAR"/>
        <result column="delivery_unit" property="deliveryUnit" jdbcType="INTEGER"/>
        <result column="delivery_upper_limit" property="deliveryUpperLimit" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="auto_calculate" property="autoCalculate" jdbcType="TINYINT"/>
        <result column="delivery_period" property="deliveryPeriod" jdbcType="INTEGER"/>
        <result column="delivery_start_type" property="deliveryStartType" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="threshold" property="threshold" jdbcType="INTEGER"/>
        <result column="plus_day" property="plusDay" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, timing_sku, area_no, display, start_time, end_time, delivery_start,
        delivery_end, rule_information, delivery_unit, delivery_upper_limit, update_time,
        type, priority, auto_calculate, delivery_period, delivery_start_type, create_time,
        threshold, plus_day
    </sql>
    <!-- 批量插入定期送规则 -->
    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO timing_rule (
            name, timing_sku, area_no, display, start_time, end_time, delivery_start,
            delivery_end, rule_information, delivery_unit, delivery_upper_limit, type,
            priority, auto_calculate, delivery_period, delivery_start_type, threshold,
            plus_day, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.name,jdbcType=VARCHAR},
                #{item.timingSku,jdbcType=VARCHAR},
                #{item.areaNo,jdbcType=INTEGER},
                #{item.display,jdbcType=TINYINT},
                #{item.startTime,jdbcType=TIMESTAMP},
                #{item.endTime,jdbcType=TIMESTAMP},
                #{item.deliveryStart,jdbcType=DATE},
                #{item.deliveryEnd,jdbcType=DATE},
                #{item.ruleInformation,jdbcType=VARCHAR},
                #{item.deliveryUnit,jdbcType=INTEGER},
                #{item.deliveryUpperLimit,jdbcType=INTEGER},
                #{item.type,jdbcType=INTEGER},
                #{item.priority,jdbcType=INTEGER},
                #{item.autoCalculate,jdbcType=TINYINT},
                #{item.deliveryPeriod,jdbcType=INTEGER},
                #{item.deliveryStartType,jdbcType=INTEGER},
                #{item.threshold,jdbcType=INTEGER},
                #{item.plusDay,jdbcType=INTEGER},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

    <!-- 根据区域和SKU查询 -->
    <select id="selectByAreaAndSkus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM timing_rule
        WHERE area_no = #{areaNo} AND timing_sku in
        <foreach item="item" collection="skus" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY priority ASC, create_time DESC
    </select>

</mapper>
