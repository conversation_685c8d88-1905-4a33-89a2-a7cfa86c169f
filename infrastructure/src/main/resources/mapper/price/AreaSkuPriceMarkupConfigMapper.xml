<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.price.AreaSkuPriceMarkupConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="areaSkuPriceMarkupConfigResultMap" type="net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="markup_type" property="markupType" jdbcType="INTEGER"/>
		<result column="markup_value" property="markupValue" jdbcType="DOUBLE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="areaSkuPriceMarkupConfigColumns">
          t.id,
          t.sku,
          t.area_no,
          t.markup_type,
          t.markup_value,
          t.update_time,
          t.create_time,
          t.creator,
          t.updater
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="ids != null and ids.size() > 0">
                and t.id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
            <if test="areaNos != null and areaNos.size() > 0">
                and t.area_no in
                <foreach collection="areaNos" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="markupType != null">
                    t.markup_type = #{markupType},
                </if>
                <if test="markupValue != null">
                    t.markup_value = #{markupValue},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="areaSkuPriceMarkupConfigResultMap" >
        SELECT <include refid="areaSkuPriceMarkupConfigColumns" />
        FROM area_sku_price_markup_config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam"  resultType="net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity" >
        SELECT
        t.id id,
        t.sku sku,
        t.area_no areaNo,
        a.area_name  as areaName,
        p.pd_name  as pdName,
        i.weight  as specification,
        t.markup_type markuptType,
        t.markup_value  markupValue,
        ak.price  as salePrice,
        t.update_time updateTime,
        t.create_time createTime,
        t.creator ,
        t.updater
        FROM area_sku_price_markup_config t
        left join area_sku ak on t.sku = ak.sku  and t.area_no = ak.area_no
        left join inventory i on i.sku = t.sku
        left join products p on p.pd_id = i.pd_id
        left join area a on a.area_no = t.area_no
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                and t.id = #{id}
            </if>
            <if test="sku != null and sku !=''">
                and t.sku = #{sku}
            </if>
            <if test="areaNo != null">
                and t.area_no = #{areaNo}
            </if>
            <if test = "pdName != null">
                and p.pd_name like CONCAT(#{pdName},'%')
            </if>
            <if test="categoryIds != null and categoryIds.size() > 0">
                and p.category_id in
                <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
        ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam" resultMap="areaSkuPriceMarkupConfigResultMap" >
        SELECT <include refid="areaSkuPriceMarkupConfigColumns" />
        FROM area_sku_price_markup_config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO area_sku_price_markup_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="areaNo != null">
				  area_no,
              </if>
              <if test="markupType != null">
				  markup_type,
              </if>
              <if test="markupValue != null">
				  markup_value,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="updater != null">
				  updater,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="areaNo != null">
				#{areaNo,jdbcType=INTEGER},
              </if>
              <if test="markupType != null">
				#{markupType,jdbcType=INTEGER},
              </if>
              <if test="markupValue != null">
				#{markupValue,jdbcType=DOUBLE},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig" >
        UPDATE area_sku_price_markup_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>

    <insert id="batchInsert" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO area_sku_price_markup_config (
        sku,
        area_no,
        markup_type,
        markup_value,
        create_time,
        update_time,
        creator,
        updater
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.sku,jdbcType=VARCHAR},
            #{item.areaNo,jdbcType=INTEGER},
            #{item.markupType,jdbcType=INTEGER},
            #{item.markupValue,jdbcType=DECIMAL},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.creator,jdbcType=VARCHAR},
            #{item.updater,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


    <!-- 根据主键ID进行修改 -->
    <update id="updateMarkupValueByIds"  >
        UPDATE area_sku_price_markup_config t
        set t.markup_value = #{markupValue}
        where t.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig" >
        DELETE FROM area_sku_price_markup_config
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>