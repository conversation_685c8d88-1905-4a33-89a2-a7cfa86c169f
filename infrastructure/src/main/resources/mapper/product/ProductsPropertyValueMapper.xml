<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.ProductsPropertyValueMapper">


  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="pdId != null">
        t.pd_id = #{pdId},
      </if>
      <if test="sku != null">
        t.sku = #{sku},
      </if>
      <if test="productsPropertyId != null">
        t.products_property_id = #{productsPropertyId},
      </if>
      <if test="productsPropertyValue != null">
        t.products_property_value = #{productsPropertyValue},
      </if>
      <if test="creator != null">
        t.creator = #{creator},
      </if>
      <if test="createTime != null">
        t.create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
    </trim>
  </sql>

    <delete id="deleteByPdId">
      delete from products_property_value where pd_id = #{pdId,jdbcType=BIGINT} and sku is null
    </delete>
    <delete id="deleteByPdIdAndPropertyIds">
      delete from products_property_value
      where pd_id = #{pdId} and
      sku is not null and
      products_property_id not in
      <foreach collection="productsPropertyIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>

    </delete>

    <select id="listByPdIds" resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
    select ppv.id,
    pd_id pdId,
    sku,
    ppv.products_property_id productsPropertyId,
    ppv.products_property_value productsPropertyValue,
    name
    from products_property_value ppv
    left join products_property pp on ppv.products_property_id = pp.id
    <where>
      pp.status = 1 and ppv.sku is null
      <if test="pdIds != null and pdIds.size > 0">
        and ppv.pd_id in
        <foreach collection="pdIds" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="propertyIds != null and propertyIds.size > 0">
        and ppv.products_property_id in
        <foreach collection="propertyIds" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

    <select id="listByConditions"
            resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
      select sku, products_property_id productsPropertyId, products_property_value productsPropertyValue
      from products_property pp
      left join products_property_value ppv on pp.id = ppv.products_property_id
      where ppv.pd_id = #{pdId}
      and ppv.sku in
      <foreach collection="skus" item="sku" open="(" close=")" separator=",">
        #{sku}
      </foreach>
      and pp.type = #{type}
    </select>
  <select id="selectSaleValueBySku"
          resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
    select ppv.id,
           ppv.pd_id                   pdId,
           ppv.sku,
           ppv.products_property_id    productsPropertyId,
           ppv.products_property_value productsPropertyValue,
           pp.name
    from products_property_value ppv
           left join products_property pp on ppv.products_property_id = pp.id
    where ppv.sku = #{sku} and pp.status = 1
    order by pp.create_time
  </select>
  <select id="listBySkuAndPdid"
          resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
    select ppv.id,
    ppv.pd_id                   pdId,
    ppv.sku,
    ppv.products_property_id    productsPropertyId,
    ppv.products_property_value productsPropertyValue,
    ppv.creator,
    ppv.create_time         createTime,
    pp.name,
    pp.format_str formatStr,
    pp.type type,
    pp.format_type formatType
    from products_property_value ppv
    left join products_property pp on ppv.products_property_id = pp.id
    <where>
      pp.status = 1
      <if test="pdId != null">
        and ppv.pd_id = #{pdId}
      </if>
      <choose>
        <when test="sku != null">
          and ppv.sku = #{sku}
        </when>
        <otherwise>
          and ppv.sku is null
        </otherwise>
      </choose>
    </where>
    order by pp.create_time
  </select>

    <select id="selectPdNoByKeyValue" resultType="java.lang.String">
      select p.pd_no from products_property_value ppv
                            left join products p on ppv.pd_id = p.pd_id
      where p.category_id = #{categoryId}
        and p.create_type = #{createType}
        and ppv.products_property_id = #{productsPropertyId}
        and ppv.products_property_value = #{productsPropertyValue}
        and p.outdated  in(-1,0)
        and p.audit_status in(0,1)
    </select>

    <insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsPropertyValue" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO products_property_value
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="productsPropertyId != null">
        products_property_id,
      </if>
      <if test="productsPropertyValue != null">
        products_property_value,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="productsPropertyId != null">
        #{productsPropertyId,jdbcType=INTEGER},
      </if>
      <if test="productsPropertyValue != null">
        #{productsPropertyValue,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <!-- 根据主键ID进行修改 -->
  <update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsPropertyValue" >
    UPDATE products_property_value t
    <include refid="whereColumnByUpdate"></include>
    <where>
      t.id = #{id,jdbcType=INTEGER}
    </where>
  </update>

  <select id="selectSaleValueBySkuList"
          resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
    select
     sku,
     products_property_id productsPropertyId,
     products_property_value productsPropertyValue
    from
     products_property pp
    left join products_property_value ppv on pp.id = ppv.products_property_id
    where
      ppv.sku in
        <foreach collection = "skuList" item = "sku" open = "(" close = ")" separator = "," >
            #{sku}
        </foreach>
  </select>
  <select id="selectSaleValueBySkuListAndPropertyIds"
          resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
    select
      sku,
      products_property_id productsPropertyId,
      products_property_value productsPropertyValue
    from products_property_value
    where sku in
    <foreach collection = "skuList" item = "sku" open = "(" close = ")" separator = "," >
      #{sku}
    </foreach>
    <if test="propertyIds != null and propertyIds.size > 0">
      and products_property_id in
      <foreach collection="propertyIds" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="selectSkusByKeyValue" resultType="java.lang.String">
    select DISTINCT(sku)
    from products_property_value
    where products_property_id = (select id from products_property where status = #{status} and `name` = #{name} and `type` = #{type})
    and sku is not null and products_property_value in
    <foreach collection = "productsPropertyValues" item = "v" open = "(" close = ")" separator = "," >
      #{v}
    </foreach>
  </select>
  <select id="selectByPdId"
          resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity">
    select
      id,
      pd_id pdId,
      sku,
      products_property_id productsPropertyId,
      products_property_value productsPropertyValue,
      creator,
      create_time createTime,
      update_time updateTime
    from products_property_value
    where pd_id = #{pdId}
  </select>
</mapper>