<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.GoodsLocationDetailMapper">
    <!-- 结果集映射 -->
    <resultMap id="goodsLocationDetailResultMap" type="net.summerfarm.manage.infrastructure.model.product.GoodsLocationDetail">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="gl_no" property="glNo" jdbcType="VARCHAR"/>
		<result column="batch" property="batch" jdbcType="VARCHAR"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="quality_date" property="qualityDate" jdbcType="DATE"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="sale_lock_quantity" property="saleLockQuantity" jdbcType="INTEGER"/>
		<result column="production_date" property="productionDate" jdbcType="DATE"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="goodsLocationDetailColumns">
          t.id,
          t.gl_no,
          t.batch,
          t.sku,
          t.quality_date,
          t.quantity,
          t.add_time,
          t.update_time,
          t.status,
          t.sale_lock_quantity,
          t.production_date
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="glNo != null and glNo !=''">
                AND t.gl_no = #{glNo}
            </if>
			<if test="batch != null and batch !=''">
                AND t.batch = #{batch}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="qualityDate != null">
                AND t.quality_date = #{qualityDate}
            </if>
			<if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="saleLockQuantity != null">
                AND t.sale_lock_quantity = #{saleLockQuantity}
            </if>
			<if test="productionDate != null">
                AND t.production_date = #{productionDate}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="glNo != null">
                    t.gl_no = #{glNo},
                </if>
                <if test="batch != null">
                    t.batch = #{batch},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="qualityDate != null">
                    t.quality_date = #{qualityDate},
                </if>
                <if test="quantity != null">
                    t.quantity = #{quantity},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="saleLockQuantity != null">
                    t.sale_lock_quantity = #{saleLockQuantity},
                </if>
                <if test="productionDate != null">
                    t.production_date = #{productionDate},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="goodsLocationDetailResultMap" >
        SELECT <include refid="goodsLocationDetailColumns" />
        FROM goods_location_detail t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.GoodsLocationDetailQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity" >
        SELECT
            t.id id,
            t.gl_no glNo,
            t.batch batch,
            t.sku sku,
            t.quality_date qualityDate,
            t.quantity quantity,
            t.add_time addTime,
            t.update_time updateTime,
            t.status status,
            t.sale_lock_quantity saleLockQuantity,
            t.production_date productionDate
        FROM goods_location_detail t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.GoodsLocationDetailQueryParam" resultMap="goodsLocationDetailResultMap" >
        SELECT <include refid="goodsLocationDetailColumns" />
        FROM goods_location_detail t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectBySkus"
            resultType="net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity">
        select gld.id, gld.gl_no glNo, gld.batch, gld.sku, gld.quality_date qualityDate, gld.quantity, gld.add_time addTime,gld.update_time updateTime, gld.status
             , (gld.quantity - gld.sale_lock_quantity) approveQuantity
             ,gld.production_date productionDate, gld.sale_lock_quantity saleLockQuantity
        from goods_location_detail gld
                 left join purchases_plan pp on pp.purchase_no = gld.batch and pp.sku = gld.sku  and pp.quality_date = gld.quality_date and pp.origin_id is not null and pp.plan_status = 1
                 left join purchases p on p.purchase_no = gld.batch
        where gld.sku in
        <foreach collection="skus" item="sku" index="index" open="(" close=")" separator=",">
            #{sku}
        </foreach>
         and gld.status = 0 and  gld.quantity > 0
        order by gld.quality_date , p.purchase_time
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.GoodsLocationDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO goods_location_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="glNo != null">
				  gl_no,
              </if>
              <if test="batch != null">
				  batch,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="qualityDate != null">
				  quality_date,
              </if>
              <if test="quantity != null">
				  quantity,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="saleLockQuantity != null">
				  sale_lock_quantity,
              </if>
              <if test="productionDate != null">
				  production_date,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="glNo != null">
				#{glNo,jdbcType=VARCHAR},
              </if>
              <if test="batch != null">
				#{batch,jdbcType=VARCHAR},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="qualityDate != null">
				#{qualityDate,jdbcType=DATE},
              </if>
              <if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="saleLockQuantity != null">
				#{saleLockQuantity,jdbcType=INTEGER},
              </if>
              <if test="productionDate != null">
				#{productionDate,jdbcType=DATE},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.GoodsLocationDetail" >
        UPDATE goods_location_detail t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.GoodsLocationDetail" >
        DELETE FROM goods_location_detail t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>