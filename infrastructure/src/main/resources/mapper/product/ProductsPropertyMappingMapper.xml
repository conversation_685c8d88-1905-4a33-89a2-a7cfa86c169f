<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.ProductsPropertyMappingMapper">
    <!-- 结果集映射 -->
    <resultMap id="productsPropertyMappingResultMap" type="net.summerfarm.manage.infrastructure.model.product.ProductsPropertyMapping">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="mapping_id" property="mappingId" jdbcType="INTEGER"/>
		<result column="products_property_id" property="productsPropertyId" jdbcType="INTEGER"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="productsPropertyMappingColumns">
          t.id,
          t.type,
          t.mapping_id,
          t.products_property_id,
          t.creator,
          t.create_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="mappingId != null">
                AND t.mapping_id = #{mappingId}
            </if>
			<if test="productsPropertyId != null">
                AND t.products_property_id = #{productsPropertyId}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="mappingId != null">
                    t.mapping_id = #{mappingId},
                </if>
                <if test="productsPropertyId != null">
                    t.products_property_id = #{productsPropertyId},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="productsPropertyMappingResultMap" >
        SELECT <include refid="productsPropertyMappingColumns" />
        FROM products_property_mapping t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.ProductsPropertyMappingQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingEntity" >
        SELECT
            t.id id,
            t.type type,
            t.mapping_id mappingId,
            t.products_property_id productsPropertyId,
            t.creator creator,
            t.create_time createTime
        FROM products_property_mapping t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.ProductsPropertyMappingQueryParam" resultMap="productsPropertyMappingResultMap" >
        SELECT <include refid="productsPropertyMappingColumns" />
        FROM products_property_mapping t
        <include refid="whereColumnBySelect"></include>
    </select>

    <select id="selectAnchoredProperty"
            resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity">
        select pp.id,
               pp.name,
               pp.type,
               pp.format_type,
               pp.format_str,
               pp.status,
               pp.creator,
               pp.create_time
        from products_property pp
                 left join products_property_mapping ppm on pp.id = ppm.products_property_id
        where pp.status = 1 and ppm.type = #{type} and ppm.mapping_id = #{mappingId}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsPropertyMapping" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO products_property_mapping
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="mappingId != null">
				  mapping_id,
              </if>
              <if test="productsPropertyId != null">
				  products_property_id,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="mappingId != null">
				#{mappingId,jdbcType=INTEGER},
              </if>
              <if test="productsPropertyId != null">
				#{productsPropertyId,jdbcType=INTEGER},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsPropertyMapping" >
        UPDATE products_property_mapping t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsPropertyMapping" >
        DELETE FROM products_property_mapping t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteBySelective">
        delete from products_property_mapping
        where `type` = #{type,jdbcType=INTEGER} and products_property_id = #{productsPropertyId,jdbcType=INTEGER} and mapping_id IN
        <foreach collection="mappingIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="listProductsPropertyMapping"
            resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingRelationInfo">
        select
            ppm.id,
            ppm.type,
            ppm.mapping_id as mappingId,
            ppm.products_property_id as productsPropertyId,
            pp.name as productsPropertyName,
            ppm.creator,
            ppm.create_time as create_time
        from
            `products_property_mapping` ppm
                INNER JOIN `products_property` pp on ppm.`products_property_id` = pp.id
        <where>
            <if test="propertyType != null">
                pp.`type` = #{propertyType,jdbcType=INTEGER}
            </if>
            <if test="mappingType != null">
                and ppm.`type` = #{mappingType,jdbcType=INTEGER}
            </if>
            <if test="mappingIdList != null and mappingIdList.size > 0">
                and ppm.mapping_id in
                <foreach collection="mappingIdList" item="mappingId" open="(" separator="," close=")">
                    #{mappingId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>