<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.InventoryBindMapper">
    <!-- 结果集映射 -->
    <resultMap id="inventoryBindResultMap" type="net.summerfarm.manage.infrastructure.model.product.InventoryBind">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="pd_id" property="pdId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="bind_sku" property="bindSku" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="inventoryBindColumns">
          t.id,
          t.pd_id,
          t.sku,
          t.bind_sku,
          t.create_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="pdId != null">
                AND t.pd_id = #{pdId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="bindSku != null and bindSku !=''">
                AND t.bind_sku = #{bindSku}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="pdId != null">
                    t.pd_id = #{pdId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="bindSku != null">
                    t.bind_sku = #{bindSku},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="inventoryBindResultMap" >
        SELECT <include refid="inventoryBindColumns" />
        FROM inventory_bind t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.InventoryBindEntity" >
        SELECT
            t.id id,
            t.pd_id pdId,
            t.sku sku,
            t.bind_sku bindSku,
            t.create_time createTime,
            t.update_time updateTime
        FROM inventory_bind t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam" resultMap="inventoryBindResultMap" >
        SELECT <include refid="inventoryBindColumns" />
        FROM inventory_bind t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectOneByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam"
            resultMap="inventoryBindResultMap">
        SELECT <include refid="inventoryBindColumns" />
        FROM inventory_bind t
        <where>
            <if test="sku != null">
                and t.sku = #{sku,jdbcType=VARCHAR}
            </if>
            <if test="bindSku != null">
                and t.bind_sku = #{bindSku,jdbcType=VARCHAR}
            </if>
            <if test="pdId != null">
                and t.pd_id = #{pdId,jdbcType=NUMERIC}
            </if>
        </where>
        limit 1
    </select>

    <select id="selectByBindSkuAndExtType" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam"
            resultMap="inventoryBindResultMap">
        SELECT <include refid="inventoryBindColumns" />
        from inventory_bind t join inventory i on t.sku = i.sku
        where t.pd_id = #{pdId} and bind_sku = #{bindSku} and i.ext_type = #{extType} and i.outdated = 0
        limit 1
    </select>
    <select id="selectByPdIdAndExtType" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam"
            resultMap="inventoryBindResultMap">
        SELECT <include refid="inventoryBindColumns" />
        from inventory_bind t join inventory i on t.sku = i.sku
        where t.pd_id=#{pdId,jdbcType=BIGINT} and i.ext_type = #{extType}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.InventoryBind" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO inventory_bind
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="pdId != null">
				  pd_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="bindSku != null">
				  bind_sku,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="pdId != null">
				#{pdId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="bindSku != null">
				#{bindSku,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.InventoryBind" >
        UPDATE inventory_bind t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.InventoryBind" >
        DELETE FROM inventory_bind t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>