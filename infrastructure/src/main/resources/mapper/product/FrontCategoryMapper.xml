<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.FrontCategoryMapper">
    <!-- 结果集映射 -->
    <resultMap id="frontCategoryResultMap" type="net.summerfarm.manage.infrastructure.model.product.FrontCategory">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="parent_id" property="parentId" jdbcType="INTEGER"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="outdated" property="outdated" jdbcType="INTEGER"/>
		<result column="icon" property="icon" jdbcType="VARCHAR"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="f_category_type" property="fCategoryType" jdbcType="TINYINT"/>
    </resultMap>


    <resultMap id="frontCategoryEntityResultMap" type="net.summerfarm.manage.domain.product.entity.FrontCategoryEntity">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="outdated" property="outdated" jdbcType="INTEGER"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="f_category_type" property="fCategoryType" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="frontCategoryColumns">
          t.id,
          t.parent_id,
          t.name,
          t.outdated,
          t.icon,
          t.updater,
          t.update_time,
          t.creator,
          t.create_time,
          t.f_category_type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="parentId != null">
                AND t.parent_id = #{parentId}
            </if>
			<if test="name != null and name !=''">
                AND t.name = #{name}
            </if>
			<if test="outdated != null">
                AND t.outdated = #{outdated}
            </if>
			<if test="icon != null and icon !=''">
                AND t.icon = #{icon}
            </if>
			<if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="fCategoryType != null">
                AND t.f_category_type = #{fCategoryType}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="parentId != null">
                    t.parent_id = #{parentId},
                </if>
                <if test="name != null">
                    t.name = #{name},
                </if>
                <if test="outdated != null">
                    t.outdated = #{outdated},
                </if>
                <if test="icon != null">
                    t.icon = #{icon},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="fCategoryType != null">
                    t.f_category_type = #{fCategoryType},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="frontCategoryResultMap" >
        SELECT <include refid="frontCategoryColumns" />
        FROM front_category t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.FrontCategoryQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.FrontCategoryEntity" >
        SELECT
            t.id id,
            t.parent_id parentId,
            t.name name,
            t.outdated outdated,
            t.icon icon,
            t.updater updater,
            t.update_time updateTime,
            t.creator creator,
            t.create_time createTime,
            t.f_category_type fCategoryType
        FROM front_category t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.FrontCategoryQueryParam" resultMap="frontCategoryResultMap" >
        SELECT <include refid="frontCategoryColumns" />
        FROM front_category t
        <include refid="whereColumnBySelect"></include>
    </select>


    <select id="selectAllPopCategory"  resultMap="frontCategoryEntityResultMap" >
        SELECT <include refid="frontCategoryColumns" />
        FROM front_category t where t.outdated = 0 and t.f_category_type = 2;
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.FrontCategory" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO front_category
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="parentId != null">
				  parent_id,
              </if>
              <if test="name != null">
				  name,
              </if>
              <if test="outdated != null">
				  outdated,
              </if>
              <if test="icon != null">
				  icon,
              </if>
              <if test="updater != null">
				  updater,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="fCategoryType != null">
				  f_category_type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="parentId != null">
				#{parentId,jdbcType=INTEGER},
              </if>
              <if test="name != null">
				#{name,jdbcType=VARCHAR},
              </if>
              <if test="outdated != null">
				#{outdated,jdbcType=INTEGER},
              </if>
              <if test="icon != null">
				#{icon,jdbcType=VARCHAR},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="fCategoryType != null">
				#{fCategoryType,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.FrontCategory" >
        UPDATE front_category t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.FrontCategory" >
        DELETE FROM front_category
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>