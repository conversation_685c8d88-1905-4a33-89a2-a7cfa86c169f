<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.major.MajorPriceLogMapper">
    <!-- 结果集映射 -->
    <sql id="Base_Column_List">
	id,
	sku,
	pd_name,
	weight,
	area_no,
	admin_id,
	price,
	area_name,
	direct,
	large_area_no,
	price_adjustment_value,
	remark,
	cost_price,
	mall_price,
	pay_method,
	valid_time,
	invalid_time,
	mall_show,
	price_type,
	cost,
	interest_rate,
	fixed_price,
	original_price,
	update_time,
	low_price_update_time
</sql><resultMap id="majorPriceLogResultMap" type="net.summerfarm.manage.infrastructure.model.major.MajorPriceLog">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="admin_id" property="adminId" jdbcType="NUMERIC"/>
		<result column="price" property="price" jdbcType="DOUBLE"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="direct" property="direct" jdbcType="INTEGER"/>
		<result column="large_area_no" property="largeAreaNo" jdbcType="INTEGER"/>
		<result column="price_adjustment_value" property="priceAdjustmentValue" jdbcType="DOUBLE"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="cost_price" property="costPrice" jdbcType="DOUBLE"/>
		<result column="mall_price" property="mallPrice" jdbcType="DOUBLE"/>
		<result column="pay_method" property="payMethod" jdbcType="TINYINT"/>
		<result column="valid_time" property="validTime" jdbcType="TIMESTAMP"/>
		<result column="invalid_time" property="invalidTime" jdbcType="TIMESTAMP"/>
		<result column="mall_show" property="mallShow" jdbcType="INTEGER"/>
		<result column="price_type" property="priceType" jdbcType="TINYINT"/>
		<result column="cost" property="cost" jdbcType="DOUBLE"/>
		<result column="interest_rate" property="interestRate" jdbcType="DOUBLE"/>
		<result column="fixed_price" property="fixedPrice" jdbcType="DOUBLE"/>
		<result column="original_price" property="originalPrice" jdbcType="DOUBLE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="low_price_update_time" property="lowPriceUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


        <resultMap id="majorPriceLogEntityResultMap" type="net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="admin_id" property="adminId" jdbcType="NUMERIC"/>
		<result column="price" property="price" jdbcType="DOUBLE"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="direct" property="direct" jdbcType="INTEGER"/>
		<result column="large_area_no" property="largeAreaNo" jdbcType="INTEGER"/>
		<result column="price_adjustment_value" property="priceAdjustmentValue" jdbcType="DOUBLE"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="cost_price" property="costPrice" jdbcType="DOUBLE"/>
		<result column="mall_price" property="mallPrice" jdbcType="DOUBLE"/>
		<result column="pay_method" property="payMethod" jdbcType="TINYINT"/>
		<result column="valid_time" property="validTime" jdbcType="TIMESTAMP"/>
		<result column="invalid_time" property="invalidTime" jdbcType="TIMESTAMP"/>
		<result column="mall_show" property="mallShow" jdbcType="INTEGER"/>
		<result column="price_type" property="priceType" jdbcType="TINYINT"/>
		<result column="cost" property="cost" jdbcType="DOUBLE"/>
		<result column="interest_rate" property="interestRate" jdbcType="DOUBLE"/>
		<result column="fixed_price" property="fixedPrice" jdbcType="DOUBLE"/>
		<result column="original_price" property="originalPrice" jdbcType="DOUBLE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="low_price_update_time" property="lowPriceUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="majorPriceLogColumns">
          t.id,
          t.sku,
          t.pd_name,
          t.weight,
          t.area_no,
          t.admin_id,
          t.price,
          t.area_name,
          t.direct,
          t.large_area_no,
          t.price_adjustment_value,
          t.remark,
          t.cost_price,
          t.mall_price,
          t.pay_method,
          t.valid_time,
          t.invalid_time,
          t.mall_show,
          t.price_type,
          t.cost,
          t.interest_rate,
          t.fixed_price,
          t.original_price,
          t.update_time,
          t.low_price_update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="pdName != null and pdName !=''">
                AND t.pd_name = #{pdName}
            </if>
			<if test="weight != null and weight !=''">
                AND t.weight = #{weight}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="price != null">
                AND t.price = #{price}
            </if>
			<if test="areaName != null and areaName !=''">
                AND t.area_name = #{areaName}
            </if>
			<if test="direct != null">
                AND t.direct = #{direct}
            </if>
			<if test="largeAreaNo != null">
                AND t.large_area_no = #{largeAreaNo}
            </if>
			<if test="priceAdjustmentValue != null">
                AND t.price_adjustment_value = #{priceAdjustmentValue}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
			<if test="costPrice != null">
                AND t.cost_price = #{costPrice}
            </if>
			<if test="mallPrice != null">
                AND t.mall_price = #{mallPrice}
            </if>
			<if test="payMethod != null">
                AND t.pay_method = #{payMethod}
            </if>
			<if test="validTime != null">
                AND t.valid_time = #{validTime}
            </if>
			<if test="invalidTime != null">
                AND t.invalid_time = #{invalidTime}
            </if>
			<if test="mallShow != null">
                AND t.mall_show = #{mallShow}
            </if>
			<if test="priceType != null">
                AND t.price_type = #{priceType}
            </if>
			<if test="cost != null">
                AND t.cost = #{cost}
            </if>
			<if test="interestRate != null">
                AND t.interest_rate = #{interestRate}
            </if>
			<if test="fixedPrice != null">
                AND t.fixed_price = #{fixedPrice}
            </if>
			<if test="originalPrice != null">
                AND t.original_price = #{originalPrice}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="lowPriceUpdateTime != null">
                AND t.low_price_update_time = #{lowPriceUpdateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="pdName != null">
                    t.pd_name = #{pdName},
                </if>
                <if test="weight != null">
                    t.weight = #{weight},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="price != null">
                    t.price = #{price},
                </if>
                <if test="areaName != null">
                    t.area_name = #{areaName},
                </if>
                <if test="direct != null">
                    t.direct = #{direct},
                </if>
                <if test="largeAreaNo != null">
                    t.large_area_no = #{largeAreaNo},
                </if>
                <if test="priceAdjustmentValue != null">
                    t.price_adjustment_value = #{priceAdjustmentValue},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
                <if test="costPrice != null">
                    t.cost_price = #{costPrice},
                </if>
                <if test="mallPrice != null">
                    t.mall_price = #{mallPrice},
                </if>
                <if test="payMethod != null">
                    t.pay_method = #{payMethod},
                </if>
                <if test="validTime != null">
                    t.valid_time = #{validTime},
                </if>
                <if test="invalidTime != null">
                    t.invalid_time = #{invalidTime},
                </if>
                <if test="mallShow != null">
                    t.mall_show = #{mallShow},
                </if>
                <if test="priceType != null">
                    t.price_type = #{priceType},
                </if>
                <if test="cost != null">
                    t.cost = #{cost},
                </if>
                <if test="interestRate != null">
                    t.interest_rate = #{interestRate},
                </if>
                <if test="fixedPrice != null">
                    t.fixed_price = #{fixedPrice},
                </if>
                <if test="originalPrice != null">
                    t.original_price = #{originalPrice},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="lowPriceUpdateTime != null">
                    t.low_price_update_time = #{lowPriceUpdateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="majorPriceLogResultMap" >
        SELECT <include refid="majorPriceLogColumns" />
        FROM major_price_log t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam"
            resultType="net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity">
        SELECT t.id                     id,
               t.sku                    sku,
               t.pd_name                pdName,
               t.weight                 weight,
               t.area_no                areaNo,
               t.admin_id               adminId,
               t.price                  price,
               t.area_name              areaName,
               t.direct                 direct,
               t.large_area_no          largeAreaNo,
               t.price_adjustment_value priceAdjustmentValue,
               t.remark                 remark,
               t.cost_price             costPrice,
               t.mall_price             mallPrice,
               t.pay_method             payMethod,
               t.valid_time             validTime,
               t.invalid_time           invalidTime,
               t.mall_show              mallShow,
               t.price_type             priceType,
               t.cost                   cost,
               t.interest_rate          interestRate,
               t.fixed_price            fixedPrice,
               t.original_price         originalPrice,
               t.update_time            updateTime,
               t.low_price_update_time  lowPriceUpdateTime
        FROM major_price_log t
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="sku != null and sku != ''">
                AND t.sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
            <if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
            <if test="direct != null">
                AND t.direct = #{direct}
            </if>
        </trim>
        ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam" resultMap="majorPriceLogResultMap" >
        SELECT <include refid="majorPriceLogColumns" />
        FROM major_price_log t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorPriceLog" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO major_price_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="pdName != null">
				  pd_name,
              </if>
              <if test="weight != null">
				  weight,
              </if>
              <if test="areaNo != null">
				  area_no,
              </if>
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="price != null">
				  price,
              </if>
              <if test="areaName != null">
				  area_name,
              </if>
              <if test="direct != null">
				  direct,
              </if>
              <if test="largeAreaNo != null">
				  large_area_no,
              </if>
              <if test="priceAdjustmentValue != null">
				  price_adjustment_value,
              </if>
              <if test="remark != null">
				  remark,
              </if>
              <if test="costPrice != null">
				  cost_price,
              </if>
              <if test="mallPrice != null">
				  mall_price,
              </if>
              <if test="payMethod != null">
				  pay_method,
              </if>
              <if test="validTime != null">
				  valid_time,
              </if>
              <if test="invalidTime != null">
				  invalid_time,
              </if>
              <if test="mallShow != null">
				  mall_show,
              </if>
              <if test="priceType != null">
				  price_type,
              </if>
              <if test="cost != null">
				  cost,
              </if>
              <if test="interestRate != null">
				  interest_rate,
              </if>
              <if test="fixedPrice != null">
				  fixed_price,
              </if>
              <if test="originalPrice != null">
				  original_price,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="lowPriceUpdateTime != null">
				  low_price_update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="pdName != null">
				#{pdName,jdbcType=VARCHAR},
              </if>
              <if test="weight != null">
				#{weight,jdbcType=VARCHAR},
              </if>
              <if test="areaNo != null">
				#{areaNo,jdbcType=INTEGER},
              </if>
              <if test="adminId != null">
				#{adminId,jdbcType=NUMERIC},
              </if>
              <if test="price != null">
				#{price,jdbcType=DOUBLE},
              </if>
              <if test="areaName != null">
				#{areaName,jdbcType=VARCHAR},
              </if>
              <if test="direct != null">
				#{direct,jdbcType=INTEGER},
              </if>
              <if test="largeAreaNo != null">
				#{largeAreaNo,jdbcType=INTEGER},
              </if>
              <if test="priceAdjustmentValue != null">
				#{priceAdjustmentValue,jdbcType=DOUBLE},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
              <if test="costPrice != null">
				#{costPrice,jdbcType=DOUBLE},
              </if>
              <if test="mallPrice != null">
				#{mallPrice,jdbcType=DOUBLE},
              </if>
              <if test="payMethod != null">
				#{payMethod,jdbcType=TINYINT},
              </if>
              <if test="validTime != null">
				#{validTime,jdbcType=TIMESTAMP},
              </if>
              <if test="invalidTime != null">
				#{invalidTime,jdbcType=TIMESTAMP},
              </if>
              <if test="mallShow != null">
				#{mallShow,jdbcType=INTEGER},
              </if>
              <if test="priceType != null">
				#{priceType,jdbcType=TINYINT},
              </if>
              <if test="cost != null">
				#{cost,jdbcType=DOUBLE},
              </if>
              <if test="interestRate != null">
				#{interestRate,jdbcType=DOUBLE},
              </if>
              <if test="fixedPrice != null">
				#{fixedPrice,jdbcType=DOUBLE},
              </if>
              <if test="originalPrice != null">
				#{originalPrice,jdbcType=DOUBLE},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="lowPriceUpdateTime != null">
				#{lowPriceUpdateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorPriceLog" >
        UPDATE major_price_log t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorPriceLog" >
        DELETE FROM major_price_log
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>
<!--auto generated by MybatisCodeHelper on 2025-02-20-->
    <insert id="insertList" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorPriceLog" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO major_price_log(
            sku,
            pd_name,
            weight,
            area_no,
            admin_id,
            price,
            area_name,
            direct,
            large_area_no,
            price_adjustment_value,
            remark,
            cost_price,
            mall_price,
            pay_method,
            valid_time,
            invalid_time,
            mall_show,
            price_type,
            cost,
            interest_rate,
            fixed_price,
            original_price,
            update_time,
            low_price_update_time
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
        (
            #{element.sku,jdbcType=VARCHAR},
            #{element.pdName,jdbcType=VARCHAR},
            #{element.weight,jdbcType=VARCHAR},
            #{element.areaNo,jdbcType=INTEGER},
            #{element.adminId,jdbcType=NUMERIC},
            #{element.price,jdbcType=DOUBLE},
            #{element.areaName,jdbcType=VARCHAR},
            #{element.direct,jdbcType=INTEGER},
            #{element.largeAreaNo,jdbcType=INTEGER},
            #{element.priceAdjustmentValue,jdbcType=DOUBLE},
            #{element.remark,jdbcType=VARCHAR},
            #{element.costPrice,jdbcType=DOUBLE},
            #{element.mallPrice,jdbcType=DOUBLE},
            #{element.payMethod,jdbcType=TINYINT},
            #{element.validTime,jdbcType=TIMESTAMP},
            #{element.invalidTime,jdbcType=TIMESTAMP},
            #{element.mallShow,jdbcType=INTEGER},
            #{element.priceType,jdbcType=TINYINT},
            #{element.cost,jdbcType=DOUBLE},
            #{element.interestRate,jdbcType=DOUBLE},
            #{element.fixedPrice,jdbcType=DOUBLE},
            #{element.originalPrice,jdbcType=DOUBLE},
            #{element.updateTime,jdbcType=TIMESTAMP},
            #{element.lowPriceUpdateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
</insert>



</mapper>