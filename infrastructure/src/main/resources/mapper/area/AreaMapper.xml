<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.area.AreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.manage.domain.area.entity.Area">
    <id property="id" column="id" />
    <result property="areaNo" column="area_no" />
    <result property="areaName" column="area_name" />
    <result property="adminId" column="admin_id" />
    <result property="parentNo" column="parent_no" />
    <result property="deliveryFrequent" column="delivery_frequent" />
    <result property="status" column="status" />
    <result property="deliveryFee" column="delivery_fee" />
    <result property="info" column="info" />
    <result property="address" column="address" />
    <result property="expressFee" column="express_fee" />
    <result property="deliveryRule" column="delivery_rule" />
    <result property="memberRule" column="member_rule" />
    <result property="companyAccountId" column="company_account_id" />
    <result property="poiNote" column="poi_note" />
    <result property="type" column="type" />
    <result property="freeDay" column="free_day" />
    <result property="mailToAddress" column="mail_to_address" />
    <result property="mapSection" column="map_section" />
    <result property="originAreaNo" column="origin_area_no" />
    <result property="nextDeliveryDate" column="next_delivery_date" />
    <result property="payChannel" column="pay_channel" />
    <result property="changeFlag" column="change_flag" />
    <result property="changeStoreNo" column="change_store_no" />
    <result property="changeStatus" column="change_status" />
    <result property="administrativeArea" column="administrative_area" />
    <result property="createTime" column="create_time" />
    <result property="supportAddOrder" column="support_add_order" />
    <result property="updateSupportAddOrder" column="update_support_add_order" />
    <result property="largeAreaNo" column="large_area_no" />
    <result property="grade" column="grade" />
    <result property="wxlitePayChannel" column="wxlite_pay_channel" />
    <result property="updateTime" column="update_time" />
  </resultMap>

  <resultMap id="LargeAreaResultMap" type="net.summerfarm.manage.domain.area.entity.LargeArea">
    <id property="id" column="id"/>
    <result property="largeAreaNo" column="large_area_no"/>
    <result property="largeAreaName" column="large_area_name"/>
    <result property="addTime" column="add_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="status" column="status"/>
    <result property="manageAdminId" column="manage_admin_id"/>
    <result property="adminName" column="admin_name"/>
  </resultMap>


  <sql id="Base_Column_List">
    id, area_no, area_name, admin_id, parent_no, delivery_frequent, status, delivery_fee,
    info, address, express_fee, delivery_rule, member_rule, company_account_id,
    poi_note, type, free_day, mail_to_address, map_section, origin_area_no,
    next_delivery_date, pay_channel, change_flag, change_store_no, change_status,
    administrative_area, create_time, support_add_order, update_support_add_order,
    large_area_no, grade, wxlite_pay_channel, update_time
  </sql>

  <!-- 这个SQL是可能查询所有Area的!... -->
  <select id="getNameByAreaNos" resultType="net.summerfarm.manage.domain.area.entity.Area">
    select area_no as areaNo,area_name as areaName, member_rule as memberRule, pay_channel
    payChannel, large_area_no largeAreaNo, company_account_id companyAccountId,origin_area_no
    originAreaNo,next_delivery_date nextDeliveryDate,
    change_flag changeFlag,change_store_no changeStoreNo, change_status changeStatus,map_section
    mapSection,delivery_rule deliveryRule
    ,create_time createTime,support_add_order supportAddOrder
    from area
    <where>
      <if test="areaNos !=null and areaNos.size > 0">
        area_no in <foreach collection="areaNos" item="areaNo" separator="," open="(" close=")">#{areaNo}</foreach>
      </if>
    </where>
  </select>

  <select id="listByAreaNos" resultType="net.summerfarm.manage.domain.area.entity.AreaSimpleEntity">
    select a.area_no areaNo,a.area_name areaName,a.status areaStatus,la.large_area_no
    largeAreaNo,la.large_area_name largeAreaName,la.status largeAreaStatus
    from area a
      left join large_area la on a.large_area_no=la.large_area_no
    <where>
      <if test="areaNos !=null and areaNos.size > 0">
        a.area_no in <foreach collection="areaNos" item="areaNo" separator="," open="(" close=")">#{areaNo}</foreach>
      </if>
    </where>
  </select>

  <select id="listEntitiesByLargeAreaNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from area
    where large_area_no in
    <foreach collection="largeAreaNos" item="areaNo" separator="," open="(" close=")">#{areaNo}</foreach>
  </select>

  <select id="listByLargeAreaNos"
    resultType="net.summerfarm.manage.domain.area.entity.AreaSimpleEntity">
    select a.area_no areaNo,a.area_name areaName,a.status areaStatus,la.large_area_no
    largeAreaNo,la.large_area_name largeAreaName,la.status largeAreaStatus
    from area a left join large_area la on a.large_area_no=la.large_area_no
    where a.large_area_no in
    <foreach collection="largeAreaNos" item="largeAreaNo" separator="," open="(" close=")">
      #{largeAreaNo}
    </foreach>
  </select>
  <select id="selectAllMemberRulesValidArea"
    resultType="net.summerfarm.manage.domain.area.entity.Area">
    select
    area_no as areaNo,
    area_name as areaName,
    member_rule as memberRule
    from area
    where status = 1
  </select>

  <select id="queryAllLargeArea" resultMap="LargeAreaResultMap">
    select
    id, large_area_no, large_area_name, add_time, update_time, status, manage_admin_id, admin_name
    from large_area
    <where>
      <if test="status !=null">status=#{status}</if>
    </where>
  </select>
  <select id="queryCloseAreaNos" resultType="java.lang.Integer">
    select area_no from area where status = 0
  </select>
  <select id="queryLargeAreaByLargeAreaNos" resultMap="LargeAreaResultMap">
    select
    id, large_area_no, large_area_name, add_time, update_time, status, manage_admin_id, admin_name
    from large_area
    where large_area_no in
    <foreach collection="nos" item="largeAreaNo" separator="," open="(" close=")">
    #{largeAreaNo}
    </foreach>
  </select>
  <select id="listByAreaNames" resultType="net.summerfarm.manage.domain.area.entity.AreaSimpleEntity">
    select a.area_no areaNo,a.area_name areaName,a.status areaStatus,la.large_area_no
    largeAreaNo,la.large_area_name largeAreaName,la.status largeAreaStatus
    from area a
    left join large_area la on a.large_area_no=la.large_area_no
    <where>
      a.status = 1
      <if test="names !=null and names.size > 0">
       and a.area_name in <foreach collection="names" item="areaName" separator="," open="(" close=")">#{areaName}</foreach>
      </if>
    </where>
  </select>

  <select id="batchQueryLargeAreaInfoByLargeAreaNos"
            resultType="net.summerfarm.manage.domain.area.entity.AreaSimpleEntity">
    select large_area_no largeAreaNo,large_area_name largeAreaName
    from large_area
    where large_area_no in
    <foreach collection="largeAreaNos" item="largeAreaNo" open="(" separator="," close=")">
      #{largeAreaNo}
    </foreach>
  </select>

</mapper>