<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantCancelMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantCancelResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.MerchantCancel">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="status" property="status" jdbcType="TINYINT"/>
		<result column="remake" property="remake" jdbcType="VARCHAR"/>
		<result column="certificate" property="certificate" jdbcType="VARCHAR"/>
		<result column="creator" property="creator" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="updater" property="updater" jdbcType="NUMERIC"/>
		<result column="resource" property="resource" jdbcType="TINYINT"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="mname" property="mname" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantCancelColumns">
          t.id,
          t.m_id,
          t.status,
          t.remake,
          t.certificate,
          t.creator,
          t.create_time,
          t.update_time,
          t.updater,
          t.resource,
          t.phone,
          t.mname,
          t.area_no
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="remake != null and remake !=''">
                AND t.remake = #{remake}
            </if>
			<if test="certificate != null and certificate !=''">
                AND t.certificate = #{certificate}
            </if>
			<if test="creator != null">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="updater != null">
                AND t.updater = #{updater}
            </if>
			<if test="resource != null">
                AND t.resource = #{resource}
            </if>
			<if test="phone != null and phone !=''">
                AND t.phone = #{phone}
            </if>
			<if test="mname != null and mname !=''">
                AND t.mname = #{mname}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
        </trim>
    </sql>


	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="merchantCancelResultMap" >
        SELECT <include refid="merchantCancelColumns" />
        FROM merchant_cancel t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam"  resultType="net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity" >
        select mc.m_id mId, mc.mname, mc.phone phone,
        mc.id id, mc.area_no areaNo, mc.status status, mc.create_time createTime
        from merchant_cancel mc
        <where>
            <if test="status != null">
                and mc.status = #{status,jdbcType=TINYINT}
            </if>
            <if test="phone != null">
                and mc.phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="areaNo != null">
                and mc.area_no = #{areaNo,jdbcType=INTEGER}
            </if>
            <if test="mname != null">
                and mc.mname like CONCAT(#{mname},'%')
            </if>
            <if test="endTime != null">
                and #{endTime} >= mc.create_time
            </if>
            <if test="startTime != null">
                and mc.create_time >= #{startTime}
            </if>
        </where>
        order by id desc
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam" resultMap="merchantCancelResultMap" >
        SELECT <include refid="merchantCancelColumns" />
        FROM merchant_cancel t
        <include refid="whereColumnBySelect"></include>
    </select>




</mapper>