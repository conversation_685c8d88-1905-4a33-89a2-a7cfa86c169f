<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.ContactMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.Contact">
        <id column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="contact" property="contact" jdbcType="VARCHAR"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="BIT"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="weixincode" property="weixincode" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="delivery_car" property="deliveryCar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_default" property="isDefault" jdbcType="INTEGER"/>
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
        <result column="distance" property="distance" jdbcType="DECIMAL" />
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="house_number" property="houseNumber"/>
        <result column="store_no" property="storeNo"/>
        <result column="delivery_frequent" property="deliveryFrequent"/>
        <result column="delivery_rule" property="deliveryRule" jdbcType="VARCHAR"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="address_remark" property="addressRemark" jdbcType="VARCHAR"/>

    </resultMap>


    <sql id="Base_Column_List">
    contact_id, m_id, contact, position, gender, phone,
     email, weixincode,province,city,area,address,status,remark,is_default,delivery_car,poi_note,distance,path,house_number,store_no,
    delivery_frequent, delivery_rule, delivery_fee,address_remark
  </sql>



    <insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.Contact" keyColumn="contact_id" keyProperty="contactId" useGeneratedKeys="true">
        insert into contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="contact != null">
                contact,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="weixincode != null">
                weixincode,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isDefault != null">
                is_default,
            </if>
            <if test="poiNote != null">
                poi_note,
            </if>
            <if test="distance != null">
                distance,
            </if>
            <if test="houseNumber != null">
                house_number ,
            </if>
            <if test="storeNo != null">
                store_no ,
            </if>
            <if test="deliveryFrequent != null">
                delivery_frequent,
            </if>
            <if test="deliveryRule != null">
                delivery_rule,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="addressRemark != null">
                address_remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="contact != null">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=BIT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="weixincode != null">
                #{weixincode,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province},
            </if>
            <if test="city != null">
                #{city},
            </if>
            <if test="area != null">
                #{area},
            </if>
            <if test="address != null">
                #{address},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="isDefault != null">
                #{isDefault},
            </if>
            <if test="poiNote != null">
                #{poiNote},
            </if>
            <if test="distance != null">
                #{distance},
            </if>
            <if test="houseNumber != null">
                #{houseNumber},
            </if>
            <if test="storeNo != null">
                #{storeNo} ,
            </if>
            <if test="deliveryFrequent != null">
                #{deliveryFrequent},
            </if>
            <if test="deliveryRule != null">
                #{deliveryRule},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee},
            </if>
            <if test="addressRemark != null">
                #{addressRemark},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.Contact">
        update contact
        <set>
            <if test="contact != null">
                contact = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province=#{province},
            </if>
            <if test="city != null">
                city=#{city},
            </if>
            <if test="area != null">
                area=#{area},
            </if>
            <if test="address != null">
                address=#{address},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="remark != null">
                remark=#{remark},
            </if>
            <if test="deliveryCar != null">
                delivery_car=#{deliveryCar},
            </if>
            <if test="isDefault != null">
                is_default=#{isDefault},
            </if>
            <if test="poiNote != null">
                poi_note = #{poiNote},
            </if>
            <if test="distance != null">
                distance = #{distance},
            </if>
            <if test="path != null">
                path = #{path},
            </if>
            <if test="houseNumber != null">
                house_number =#{houseNumber} ,
            </if>
            <if test="storeNo != null">
                store_no =#{storeNo} ,
            </if>
            <if test="deliveryFrequent !=null">
                delivery_frequent = #{deliveryFrequent},
            </if>
            <if test="deliveryRule != null">
                delivery_rule = #{deliveryRule},
            </if>
            <if test="deliveryFee != null">
                delivery_fee = #{deliveryFee},
            </if>
            <if test="addressRemark != null">
                address_remark = #{addressRemark},
            </if>

        </set>
        where contact_id = #{contactId,jdbcType=BIGINT}
    </update>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM contact
        WHERE contact_id = #{contactId,jdbcType=BIGINT}
    </select>


    <select id="selectByPrimaryKeyList"  resultType="net.summerfarm.manage.domain.merchant.entity.ContactEntity">
        SELECT contact_id contactId,m_id mId,contact,position,gender,phone,email,weixincode,province,city,area,address,delivery_car deliveryCar,status,
               remark,is_default isDefault,poi_note poiNote,distance,path,store_no storeNo, delivery_frequent deliveryFrequent, delivery_rule deliveryRule, delivery_fee deliveryFee,address_remark addressRemark
        FROM contact
        WHERE contact_id in
        <foreach collection="contactIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectDefaultContactByMid" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from contact
        where status = 1
         and m_id = #{mId}
         order by is_default desc
         limit 1
  </select>


</mapper>