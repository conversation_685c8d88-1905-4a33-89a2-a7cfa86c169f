<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantSkuOrderDataMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantSkuOrderDataResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.MerchantSkuOrderData">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
		<result column="last_order_quantity" property="lastOrderQuantity" jdbcType="INTEGER"/>
		<result column="last_thirty_days_order_count" property="lastThirtyDaysOrderCount" jdbcType="INTEGER"/>
		<result column="last_sixty_days_order_count" property="lastSixtyDaysOrderCount" jdbcType="INTEGER"/>
		<result column="last_two_years_order_count" property="lastTwoYearsOrderCount" jdbcType="INTEGER"/>
		<result column="day_tag" property="dayTag" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantSkuOrderDataColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.m_id,
          t.sku,
          t.last_order_time,
          t.last_order_quantity,
          t.last_thirty_days_order_count,
          t.last_sixty_days_order_count,
          t.last_two_years_order_count,
          t.day_tag
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="lastOrderTime != null">
                AND t.last_order_time = #{lastOrderTime}
            </if>
			<if test="lastOrderQuantity != null">
                AND t.last_order_quantity = #{lastOrderQuantity}
            </if>
			<if test="lastThirtyDaysOrderCount != null">
                AND t.last_thirty_days_order_count = #{lastThirtyDaysOrderCount}
            </if>
			<if test="lastSixtyDaysOrderCount != null">
                AND t.last_sixty_days_order_count = #{lastSixtyDaysOrderCount}
            </if>
			<if test="lastTwoYearsOrderCount != null">
                AND t.last_two_years_order_count = #{lastTwoYearsOrderCount}
            </if>
			<if test="dayTag != null and dayTag !=''">
                AND t.day_tag = #{dayTag}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="lastOrderTime != null">
                    t.last_order_time = #{lastOrderTime},
                </if>
                <if test="lastOrderQuantity != null">
                    t.last_order_quantity = #{lastOrderQuantity},
                </if>
                <if test="lastThirtyDaysOrderCount != null">
                    t.last_thirty_days_order_count = #{lastThirtyDaysOrderCount},
                </if>
                <if test="lastSixtyDaysOrderCount != null">
                    t.last_sixty_days_order_count = #{lastSixtyDaysOrderCount},
                </if>
                <if test="lastTwoYearsOrderCount != null">
                    t.last_two_years_order_count = #{lastTwoYearsOrderCount},
                </if>
                <if test="dayTag != null">
                    t.day_tag = #{dayTag},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="merchantSkuOrderDataResultMap" >
        SELECT <include refid="merchantSkuOrderDataColumns" />
        FROM merchant_sku_order_data t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantSkuOrderDataQueryParam"  resultType="net.summerfarm.manage.domain.merchant.entity.MerchantSkuOrderDataEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.m_id mId,
            t.sku sku,
            t.last_order_time lastOrderTime,
            t.last_order_quantity lastOrderQuantity,
            t.last_thirty_days_order_count lastThirtyDaysOrderCount,
            t.last_sixty_days_order_count lastSixtyDaysOrderCount,
            t.last_two_years_order_count lastTwoYearsOrderCount,
            t.day_tag dayTag
        FROM merchant_sku_order_data t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantSkuOrderDataQueryParam" resultMap="merchantSkuOrderDataResultMap" >
        SELECT <include refid="merchantSkuOrderDataColumns" />
        FROM merchant_sku_order_data t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantSkuOrderData" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO merchant_sku_order_data
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="lastOrderTime != null">
				  last_order_time,
              </if>
              <if test="lastOrderQuantity != null">
				  last_order_quantity,
              </if>
              <if test="lastThirtyDaysOrderCount != null">
				  last_thirty_days_order_count,
              </if>
              <if test="lastSixtyDaysOrderCount != null">
				  last_sixty_days_order_count,
              </if>
              <if test="lastTwoYearsOrderCount != null">
				  last_two_years_order_count,
              </if>
              <if test="dayTag != null">
				  day_tag,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="lastOrderTime != null">
				#{lastOrderTime,jdbcType=TIMESTAMP},
              </if>
              <if test="lastOrderQuantity != null">
				#{lastOrderQuantity,jdbcType=INTEGER},
              </if>
              <if test="lastThirtyDaysOrderCount != null">
				#{lastThirtyDaysOrderCount,jdbcType=INTEGER},
              </if>
              <if test="lastSixtyDaysOrderCount != null">
				#{lastSixtyDaysOrderCount,jdbcType=INTEGER},
              </if>
              <if test="lastTwoYearsOrderCount != null">
				#{lastTwoYearsOrderCount,jdbcType=INTEGER},
              </if>
              <if test="dayTag != null">
				#{dayTag,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantSkuOrderData" >
        UPDATE merchant_sku_order_data t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantSkuOrderData" >
        DELETE FROM merchant_sku_order_data t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>