<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.invocie.InvoiceConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.manage.domain.invoice.entity.InvoiceConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="open_account" jdbcType="VARCHAR" property="openAccount" />
    <result column="open_bank" jdbcType="VARCHAR" property="openBank" />
    <result column="company_address" jdbcType="VARCHAR" property="companyAddress" />
    <result column="company_phone" jdbcType="VARCHAR" property="companyPhone" />
    <result column="mail_address" jdbcType="VARCHAR" property="mailAddress" />
    <result column="company_receiver" jdbcType="VARCHAR" property="companyReceiver" />
    <result column="company_email" jdbcType="VARCHAR" property="companyEmail" />
    <result column="valid_status" jdbcType="TINYINT" property="validStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="link_method" property="linkMethod"/>
    <result column="default_flag" property="defaultFlag"/>
    <result column="business_license_address" jdbcType="VARCHAR" property="businessLicenseAddress"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_id, admin_id, `type`, invoice_title, tax_number, open_account, open_bank, 
    company_address, company_phone, mail_address, company_receiver, company_email, valid_status, 
    update_time, create_time, link_method , updater,creator, business_license_address,default_flag
  </sql>


  <select id="selectByAdminIdsType" resultType="net.summerfarm.manage.domain.invoice.entity.InvoiceConfig">
  select c.id, c.merchant_id merchantId, c.admin_id adminId, c.`type`, c.invoice_title invoiceTitle, c.tax_number taxNumber, c.open_account openAccount, c.open_bank openBank, c.
  company_address companyAddress, c. company_phone companyPhone, c. mail_address mailAddress, c. company_receiver companyReceiver, c. company_email companyEmail, c. valid_status vaildStatus, c.
  update_time updateTime, c. create_time createTime, c. link_method linkMethod , c.business_license_address businessLicenseAddress, c.creator creator , c.updater updater,
  c.default_flag defaultFlag
  FROM invoice_config c
  <where>
    <if test="type != null">
      AND c.`type` = #{type,jdbcType=TINYINT}
    </if>
    <if test="validStatus != null">
      AND c.`valid_status` = #{validStatus}
    </if>
    and c.merchant_id in
    <foreach collection="mIds" item="mid" open="(" close=")" separator=",">
      #{mid}
    </foreach>
    order by create_time desc
  </where>
  </select>



  <select id="selectByMajorAdminId"  resultType="net.summerfarm.manage.domain.invoice.entity.InvoiceConfig">
    select c.id, c.merchant_id merchantId, c.admin_id adminId, c.`type`, c.invoice_title invoiceTitle, c.tax_number taxNumber, c.open_account openAccount, c.open_bank openBank, c.
      company_address companyAddress, c. company_phone companyPhone, c. mail_address mailAddress, c. company_receiver companyReceiver, c. company_email companyEmail, c. valid_status vaildStatus, c.
             update_time updateTime, c. create_time createTime, c. link_method linkMethod , c.business_license_address businessLicenseAddress, c.creator creator , c.updater updater,
           c.default_flag defaultFlag
    FROM invoice_config c
    WHERE c.admin_id= #{adminId}
    and c.valid_status = 0 and c.type =0
  </select>

  <select id="selectByMajorByMids"  resultType="net.summerfarm.manage.domain.invoice.entity.InvoiceConfig">
    select im.id, im.invoice_id invoiceId, im.merchant_id merchantId, im.update_time updateTime, im.create_time createTime,
           i.invoice_title invoiceTitle,i.valid_status as validStatus
    from invoice_merchant_relation im
           left join invoice_config i on im.invoice_id = i.id
    where im.merchant_id in
    <foreach collection="mIds" item="mid" open="(" close=")" separator=",">
      #{mid}
    </foreach>
      and status = 0 and i.type = 1
  </select>
</mapper>