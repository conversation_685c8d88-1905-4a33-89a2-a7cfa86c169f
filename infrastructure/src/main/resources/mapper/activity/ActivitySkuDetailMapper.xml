<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.activity.ActivitySkuDetailMapper">
    <!-- 结果集映射 -->
    <resultMap id="activitySkuDetailResultMap" type="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="item_config_id" property="itemConfigId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="rounding_mode" property="roundingMode" jdbcType="TINYINT"/>
		<result column="adjust_type" property="adjustType" jdbcType="TINYINT"/>
		<result column="amount" property="amount" jdbcType="DOUBLE"/>
		<result column="sort" property="sort" jdbcType="INTEGER"/>
		<result column="plan_quantity" property="planQuantity" jdbcType="INTEGER"/>
		<result column="actual_quantity" property="actualQuantity" jdbcType="INTEGER"/>
		<result column="lock_quantity" property="lockQuantity" jdbcType="INTEGER"/>
		<result column="account_limit" property="accountLimit" jdbcType="TINYINT"/>
		<result column="limit_quantity" property="limitQuantity" jdbcType="INTEGER"/>
		<result column="min_sale_num" property="minSaleNum" jdbcType="INTEGER"/>
		<result column="single_deposit" property="singleDeposit" jdbcType="DOUBLE"/>
		<result column="expansion_ratio" property="expansionRatio" jdbcType="DOUBLE"/>
		<result column="hide_price" property="hidePrice" jdbcType="TINYINT"/>
		<result column="timing_config" property="timingConfig" jdbcType="VARCHAR"/>
		<result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="is_support_timing" property="isSupportTiming" jdbcType="TINYINT"/>
		<result column="auto_price" property="autoPrice" jdbcType="TINYINT"/>
		<result column="ladder_config" property="ladderConfig" jdbcType="VARCHAR"/>
		<result column="discount_label" property="discountLabel" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="activitySkuDetailColumns">
          t.id,
          t.item_config_id,
          t.sku,
          t.rounding_mode,
          t.adjust_type,
          t.amount,
          t.sort,
          t.plan_quantity,
          t.actual_quantity,
          t.lock_quantity,
          t.account_limit,
          t.limit_quantity,
          t.min_sale_num,
          t.single_deposit,
          t.expansion_ratio,
          t.hide_price,
          t.timing_config,
          t.del_flag,
          t.create_time,
          t.update_time,
          t.is_support_timing,
          t.auto_price,
          t.ladder_config,
          t.discount_label
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="itemConfigId != null">
                AND t.item_config_id = #{itemConfigId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="roundingMode != null">
                AND t.rounding_mode = #{roundingMode}
            </if>
			<if test="adjustType != null">
                AND t.adjust_type = #{adjustType}
            </if>
			<if test="amount != null">
                AND t.amount = #{amount}
            </if>
			<if test="sort != null">
                AND t.sort = #{sort}
            </if>
			<if test="planQuantity != null">
                AND t.plan_quantity = #{planQuantity}
            </if>
			<if test="actualQuantity != null">
                AND t.actual_quantity = #{actualQuantity}
            </if>
			<if test="lockQuantity != null">
                AND t.lock_quantity = #{lockQuantity}
            </if>
			<if test="accountLimit != null">
                AND t.account_limit = #{accountLimit}
            </if>
			<if test="limitQuantity != null">
                AND t.limit_quantity = #{limitQuantity}
            </if>
			<if test="minSaleNum != null">
                AND t.min_sale_num = #{minSaleNum}
            </if>
			<if test="singleDeposit != null">
                AND t.single_deposit = #{singleDeposit}
            </if>
			<if test="expansionRatio != null">
                AND t.expansion_ratio = #{expansionRatio}
            </if>
			<if test="hidePrice != null">
                AND t.hide_price = #{hidePrice}
            </if>
			<if test="timingConfig != null and timingConfig !=''">
                AND t.timing_config = #{timingConfig}
            </if>
			<if test="delFlag != null">
                AND t.del_flag = #{delFlag}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="isSupportTiming != null">
                AND t.is_support_timing = #{isSupportTiming}
            </if>
			<if test="autoPrice != null">
                AND t.auto_price = #{autoPrice}
            </if>
			<if test="ladderConfig != null and ladderConfig !=''">
                AND t.ladder_config = #{ladderConfig}
            </if>
			<if test="discountLabel != null">
                AND t.discount_label = #{discountLabel}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="itemConfigId != null">
                    t.item_config_id = #{itemConfigId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="roundingMode != null">
                    t.rounding_mode = #{roundingMode},
                </if>
                <if test="adjustType != null">
                    t.adjust_type = #{adjustType},
                </if>
                <if test="amount != null">
                    t.amount = #{amount},
                </if>
                <if test="sort != null">
                    t.sort = #{sort},
                </if>
                <if test="planQuantity != null">
                    t.plan_quantity = #{planQuantity},
                </if>
                <if test="actualQuantity != null">
                    t.actual_quantity = #{actualQuantity},
                </if>
                <if test="lockQuantity != null">
                    t.lock_quantity = #{lockQuantity},
                </if>
                <if test="accountLimit != null">
                    t.account_limit = #{accountLimit},
                </if>
                <if test="limitQuantity != null">
                    t.limit_quantity = #{limitQuantity},
                </if>
                <if test="minSaleNum != null">
                    t.min_sale_num = #{minSaleNum},
                </if>
                <if test="singleDeposit != null">
                    t.single_deposit = #{singleDeposit},
                </if>
                <if test="expansionRatio != null">
                    t.expansion_ratio = #{expansionRatio},
                </if>
                <if test="hidePrice != null">
                    t.hide_price = #{hidePrice},
                </if>
                <if test="timingConfig != null">
                    t.timing_config = #{timingConfig},
                </if>
                <if test="delFlag != null">
                    t.del_flag = #{delFlag},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="isSupportTiming != null">
                    t.is_support_timing = #{isSupportTiming},
                </if>
                <if test="autoPrice != null">
                    t.auto_price = #{autoPrice},
                </if>
                <if test="ladderConfig != null">
                    t.ladder_config = #{ladderConfig},
                </if>
                <if test="discountLabel != null">
                    t.discount_label = #{discountLabel},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="activitySkuDetailResultMap" >
        SELECT <include refid="activitySkuDetailColumns" />
        FROM activity_sku_detail t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.activity.param.query.ActivitySkuDetailQueryParam"  resultType="net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity" >
        SELECT
            t.id id,
            t.item_config_id itemConfigId,
            t.sku sku,
            t.rounding_mode roundingMode,
            t.adjust_type adjustType,
            t.amount amount,
            t.sort sort,
            t.plan_quantity planQuantity,
            t.actual_quantity actualQuantity,
            t.lock_quantity lockQuantity,
            t.account_limit accountLimit,
            t.limit_quantity limitQuantity,
            t.min_sale_num minSaleNum,
            t.single_deposit singleDeposit,
            t.expansion_ratio expansionRatio,
            t.hide_price hidePrice,
            t.timing_config timingConfig,
            t.del_flag delFlag,
            t.create_time createTime,
            t.update_time updateTime,
            t.is_support_timing isSupportTiming,
            t.auto_price autoPrice,
            t.ladder_config ladderConfig,
            t.discount_label discountLabel
        FROM activity_sku_detail t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.activity.param.query.ActivitySkuDetailQueryParam" resultMap="activitySkuDetailResultMap" >
        SELECT <include refid="activitySkuDetailColumns" />
        FROM activity_sku_detail t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO activity_sku_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="itemConfigId != null">
				  item_config_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="roundingMode != null">
				  rounding_mode,
              </if>
              <if test="adjustType != null">
				  adjust_type,
              </if>
              <if test="amount != null">
				  amount,
              </if>
              <if test="sort != null">
				  sort,
              </if>
              <if test="planQuantity != null">
				  plan_quantity,
              </if>
              <if test="actualQuantity != null">
				  actual_quantity,
              </if>
              <if test="lockQuantity != null">
				  lock_quantity,
              </if>
              <if test="accountLimit != null">
				  account_limit,
              </if>
              <if test="limitQuantity != null">
				  limit_quantity,
              </if>
              <if test="minSaleNum != null">
				  min_sale_num,
              </if>
              <if test="singleDeposit != null">
				  single_deposit,
              </if>
              <if test="expansionRatio != null">
				  expansion_ratio,
              </if>
              <if test="hidePrice != null">
				  hide_price,
              </if>
              <if test="timingConfig != null">
				  timing_config,
              </if>
              <if test="delFlag != null">
				  del_flag,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="isSupportTiming != null">
				  is_support_timing,
              </if>
              <if test="autoPrice != null">
				  auto_price,
              </if>
              <if test="ladderConfig != null">
				  ladder_config,
              </if>
              <if test="discountLabel != null">
				  discount_label,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="itemConfigId != null">
				#{itemConfigId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="roundingMode != null">
				#{roundingMode,jdbcType=TINYINT},
              </if>
              <if test="adjustType != null">
				#{adjustType,jdbcType=TINYINT},
              </if>
              <if test="amount != null">
				#{amount,jdbcType=DOUBLE},
              </if>
              <if test="sort != null">
				#{sort,jdbcType=INTEGER},
              </if>
              <if test="planQuantity != null">
				#{planQuantity,jdbcType=INTEGER},
              </if>
              <if test="actualQuantity != null">
				#{actualQuantity,jdbcType=INTEGER},
              </if>
              <if test="lockQuantity != null">
				#{lockQuantity,jdbcType=INTEGER},
              </if>
              <if test="accountLimit != null">
				#{accountLimit,jdbcType=TINYINT},
              </if>
              <if test="limitQuantity != null">
				#{limitQuantity,jdbcType=INTEGER},
              </if>
              <if test="minSaleNum != null">
				#{minSaleNum,jdbcType=INTEGER},
              </if>
              <if test="singleDeposit != null">
				#{singleDeposit,jdbcType=DOUBLE},
              </if>
              <if test="expansionRatio != null">
				#{expansionRatio,jdbcType=DOUBLE},
              </if>
              <if test="hidePrice != null">
				#{hidePrice,jdbcType=TINYINT},
              </if>
              <if test="timingConfig != null">
				#{timingConfig,jdbcType=VARCHAR},
              </if>
              <if test="delFlag != null">
				#{delFlag,jdbcType=TINYINT},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="isSupportTiming != null">
				#{isSupportTiming,jdbcType=TINYINT},
              </if>
              <if test="autoPrice != null">
				#{autoPrice,jdbcType=TINYINT},
              </if>
              <if test="ladderConfig != null">
				#{ladderConfig,jdbcType=VARCHAR},
              </if>
              <if test="discountLabel != null">
				#{discountLabel,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

    <!-- 批量插入活动SKU详情 -->
    <insert id="batchInsertSelective" parameterType="java.util.List">
        INSERT INTO activity_sku_detail (
            item_config_id, sku, rounding_mode, adjust_type, amount, sort,
            plan_quantity, actual_quantity, lock_quantity, account_limit, limit_quantity,
            min_sale_num, single_deposit, expansion_ratio, hide_price, timing_config,
            del_flag, is_support_timing, auto_price, ladder_config, discount_label,
            create_time, update_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.itemConfigId,jdbcType=NUMERIC},
                #{record.sku,jdbcType=VARCHAR},
                #{record.roundingMode,jdbcType=TINYINT},
                #{record.adjustType,jdbcType=TINYINT},
                #{record.amount,jdbcType=DECIMAL},
                #{record.sort,jdbcType=NUMERIC},
                #{record.planQuantity,jdbcType=NUMERIC},
                #{record.actualQuantity,jdbcType=NUMERIC},
                #{record.lockQuantity,jdbcType=NUMERIC},
                #{record.accountLimit,jdbcType=TINYINT},
                #{record.limitQuantity,jdbcType=NUMERIC},
                #{record.minSaleNum,jdbcType=NUMERIC},
                #{record.singleDeposit,jdbcType=DECIMAL},
                #{record.expansionRatio,jdbcType=DECIMAL},
                #{record.hidePrice,jdbcType=TINYINT},
                #{record.timingConfig,jdbcType=VARCHAR},
                #{record.delFlag,jdbcType=TINYINT},
                #{record.isSupportTiming,jdbcType=TINYINT},
                #{record.autoPrice,jdbcType=TINYINT},
                #{record.ladderConfig,jdbcType=VARCHAR},
                #{record.discountLabel,jdbcType=TINYINT},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail" >
        UPDATE activity_sku_detail t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail" >
        DELETE FROM activity_sku_detail
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>


    <select id="listByItemConfigs" resultMap="activitySkuDetailResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="activitySkuDetailColumns"/>
        from activity_sku_detail t
        where sku = #{sku} and del_flag = 0
        and item_config_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by item_config_id desc
    </select>


    <select id="listDetailByItemConfigs" resultType="net.summerfarm.manage.domain.activity.valueObject.ActivitySkuDetailValueObject">
        /*FORCE_MASTER*/
        select
        asd.id id ,
        asd.item_config_id itemConfigId ,
        asd.sku sku ,
        asd.del_flag delFlag ,
        asd.ladder_config ladderConfig,
        abi.create_time activityCreateTime,
        abi.id basicInfoId
        from activity_sku_detail asd
        inner join activity_item_config aic on asd.item_config_id = aic.id
        inner join activity_basic_info abi on abi.id = aic.basic_info_id
        where asd.sku = #{sku} and asd.del_flag = 0
        and asd.item_config_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByConditionV2" resultType="net.summerfarm.manage.domain.activity.valueObject.ActivitySkuDetailValueObject">
        select
            asd.id id ,
            asd.item_config_id itemConfigId ,
            asd.sku sku ,
            asd.del_flag delFlag ,
            asd.ladder_config ladderConfig,
            abi.create_time activityCreateTime,
            abi.start_time activityStartTime,
            abi.end_time activityEndTime,
            abi.name activityName,
            abi.id basicInfoId,
            asco.scope_type scopeType,
            asco.scope_id scopeId
        from activity_sku_detail asd
                 inner join activity_item_config aic on asd.item_config_id = aic.id
                 inner join activity_basic_info abi on abi.id = aic.basic_info_id
                 inner join activity_scene_config ascc on abi.id = ascc.basic_info_id
                 inner join activity_scope_config asco on abi.id = asco.basic_info_id
        where asd.del_flag = 0 and aic.del_flag = 0 and abi.del_flag = 0 and abi.status = 1 and ascc.del_flag = 0 and asco.del_flag = 0
        <if test="startTime != null">
            and abi.start_time &gt; #{startTime}
        </if>
        <if test="endTime != null">
            and abi.start_time &lt;= #{endTime}
        </if>
        <if test="isPermanent != null">
            and abi.is_permanent = #{isPermanent}
        </if>
        <if test="scopeType != null">
            and asco.scope_type = #{scopeType}
        </if>
        <if test="activityId != null">
            and abi.id = #{activityId}
        </if>
        order by abi.id desc
    </select>
    <select id="listByItemConfigsAndSkus" resultMap="activitySkuDetailResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="activitySkuDetailColumns"/>
        from activity_sku_detail t
        where del_flag = 0
        and sku in
        <foreach collection="skus" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and item_config_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by item_config_id desc
    </select>

</mapper>