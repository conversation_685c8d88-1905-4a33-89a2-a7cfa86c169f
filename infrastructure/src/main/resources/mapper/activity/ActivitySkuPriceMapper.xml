<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.activity.ActivitySkuPriceMapper">
    <!-- 结果集映射 -->
    <resultMap id="activitySkuPriceResultMap" type="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku_detail_id" property="skuDetailId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="sale_price" property="salePrice" jdbcType="DOUBLE"/>
		<result column="ladder_price" property="ladderPrice" jdbcType="VARCHAR"/>
		<result column="activity_price" property="activityPrice" jdbcType="DOUBLE"/>
		<result column="updater_id" property="updaterId" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="basic_info_id" property="basicInfoId" jdbcType="NUMERIC"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="activitySkuPriceColumns">
          t.id,
          t.sku_detail_id,
          t.sku,
          t.area_no,
          t.sale_price,
          t.ladder_price,
          t.activity_price,
          t.updater_id,
          t.create_time,
          t.update_time,
          t.basic_info_id
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="skuDetailId != null">
                AND t.sku_detail_id = #{skuDetailId}
            </if>
			<if test="skuDetailIds != null and skuDetailIds.size() > 0">
                AND t.sku_detail_id IN
                <foreach collection="skuDetailIds" item="skuDetailId" open="(" separator="," close=")">
                    #{skuDetailId}
                </foreach>
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="skus != null and skus.size() > 0">
                AND t.sku IN
                <foreach collection="skus" item="sku" open="(" separator="," close=")">
                    #{sku}
                </foreach>
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
			<if test="salePrice != null">
                AND t.sale_price = #{salePrice}
            </if>
			<if test="ladderPrice != null and ladderPrice !=''">
                AND t.ladder_price = #{ladderPrice}
            </if>
			<if test="activityPrice != null">
                AND t.activity_price = #{activityPrice}
            </if>
			<if test="updaterId != null">
                AND t.updater_id = #{updaterId}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="basicInfoId != null">
                AND t.basic_info_id = #{basicInfoId}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="skuDetailId != null">
                    t.sku_detail_id = #{skuDetailId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="salePrice != null">
                    t.sale_price = #{salePrice},
                </if>
                <if test="ladderPrice != null">
                    t.ladder_price = #{ladderPrice},
                </if>
                <if test="activityPrice != null">
                    t.activity_price = #{activityPrice},
                </if>
                <if test="updaterId != null">
                    t.updater_id = #{updaterId},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="basicInfoId != null">
                    t.basic_info_id = #{basicInfoId},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="activitySkuPriceResultMap" >
        SELECT <include refid="activitySkuPriceColumns" />
        FROM activity_sku_price t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.activity.param.query.ActivitySkuPriceQueryParam"  resultType="net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity" >
        SELECT
            t.id id,
            t.sku_detail_id skuDetailId,
            t.sku sku,
            t.area_no areaNo,
            t.sale_price salePrice,
            t.ladder_price ladderPrice,
            t.activity_price activityPrice,
            t.updater_id updaterId,
            t.create_time createTime,
            t.update_time updateTime,
            t.basic_info_id basicInfoId
        FROM activity_sku_price t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.activity.param.query.ActivitySkuPriceQueryParam" resultMap="activitySkuPriceResultMap" >
        SELECT <include refid="activitySkuPriceColumns" />
        FROM activity_sku_price t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO activity_sku_price
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="skuDetailId != null">
				  sku_detail_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="areaNo != null">
				  area_no,
              </if>
              <if test="salePrice != null">
				  sale_price,
              </if>
              <if test="ladderPrice != null">
				  ladder_price,
              </if>
              <if test="activityPrice != null">
				  activity_price,
              </if>
              <if test="updaterId != null">
				  updater_id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="basicInfoId != null">
				  basic_info_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="skuDetailId != null">
				#{skuDetailId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="areaNo != null">
				#{areaNo,jdbcType=INTEGER},
              </if>
              <if test="salePrice != null">
				#{salePrice,jdbcType=DOUBLE},
              </if>
              <if test="ladderPrice != null">
				#{ladderPrice,jdbcType=VARCHAR},
              </if>
              <if test="activityPrice != null">
				#{activityPrice,jdbcType=DOUBLE},
              </if>
              <if test="updaterId != null">
				#{updaterId,jdbcType=INTEGER},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="basicInfoId != null">
				#{basicInfoId,jdbcType=NUMERIC},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice" >
        UPDATE activity_sku_price t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice" >
        DELETE FROM activity_sku_price
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>


    <select id="selectByDetailId" resultMap="activitySkuPriceResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="activitySkuPriceColumns"/>
        from activity_sku_price t
        where `sku_detail_id` = #{skuDetailId} and sku =#{sku} and area_no =#{areaNo}
    </select>

</mapper>