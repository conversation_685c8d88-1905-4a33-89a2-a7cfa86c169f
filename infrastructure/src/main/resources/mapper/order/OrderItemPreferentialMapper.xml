<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.OrderItemPreferentialMapper">
    <!-- 结果集映射 -->
    <resultMap id="orderItemPreferentialResultMap" type="net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="amount" property="amount" jdbcType="DOUBLE"/>
		<result column="order_item_id" property="orderItemId" jdbcType="NUMERIC"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="related_id" property="relatedId" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="discounts_detail_snapshot" property="discountsDetailSnapshot" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="orderItemPreferentialColumns">
          t.id,
          t.order_no,
          t.amount,
          t.order_item_id,
          t.type,
          t.related_id,
          t.create_time,
          t.update_time,
          t.discounts_detail_snapshot
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="amount != null">
                AND t.amount = #{amount}
            </if>
			<if test="orderItemId != null">
                AND t.order_item_id = #{orderItemId}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="relatedId != null">
                AND t.related_id = #{relatedId}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="discountsDetailSnapshot != null and discountsDetailSnapshot !=''">
                AND t.discounts_detail_snapshot = #{discountsDetailSnapshot}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="amount != null">
                    t.amount = #{amount},
                </if>
                <if test="orderItemId != null">
                    t.order_item_id = #{orderItemId},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="relatedId != null">
                    t.related_id = #{relatedId},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="discountsDetailSnapshot != null">
                    t.discounts_detail_snapshot = #{discountsDetailSnapshot},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="orderItemPreferentialResultMap" >
        SELECT <include refid="orderItemPreferentialColumns" />
        FROM order_item_preferential t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.OrderItemPreferentialQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.OrderItemPreferentialEntity" >
        SELECT
            t.id id,
            t.order_no orderNo,
            t.amount amount,
            t.order_item_id orderItemId,
            t.type type,
            t.related_id relatedId,
            t.create_time createTime,
            t.update_time updateTime,
            t.discounts_detail_snapshot discountsDetailSnapshot
        FROM order_item_preferential t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.OrderItemPreferentialQueryParam" resultMap="orderItemPreferentialResultMap" >
        SELECT <include refid="orderItemPreferentialColumns" />
        FROM order_item_preferential t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO order_item_preferential
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="amount != null">
				  amount,
              </if>
              <if test="orderItemId != null">
				  order_item_id,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="relatedId != null">
				  related_id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="discountsDetailSnapshot != null">
				  discounts_detail_snapshot,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="amount != null">
				#{amount,jdbcType=DOUBLE},
              </if>
              <if test="orderItemId != null">
				#{orderItemId,jdbcType=NUMERIC},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="relatedId != null">
				#{relatedId,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="discountsDetailSnapshot != null">
				#{discountsDetailSnapshot,jdbcType=LONGVARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential" >
        UPDATE order_item_preferential t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential" >
        DELETE FROM order_item_preferential t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>