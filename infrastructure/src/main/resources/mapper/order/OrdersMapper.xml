<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.OrdersMapper">
    <!-- 结果集映射 -->
    <resultMap id="ordersResultMap" type="net.summerfarm.manage.infrastructure.model.order.Orders">
		<id column="order_id" property="orderId" jdbcType="NUMERIC"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="SMALLINT"/>
		<result column="delivery_fee" property="deliveryFee" jdbcType="DOUBLE"/>
		<result column="total_price" property="totalPrice" jdbcType="DOUBLE"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="out_times" property="outTimes" jdbcType="INTEGER"/>
		<result column="discount_type" property="discountType" jdbcType="INTEGER"/>
		<result column="out_times_fee" property="outTimesFee" jdbcType="DOUBLE"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="m_size" property="mSize" jdbcType="VARCHAR"/>
		<result column="direct" property="direct" jdbcType="INTEGER"/>
		<result column="sku_show" property="skuShow" jdbcType="INTEGER"/>
		<result column="red_pack_amount" property="redPackAmount" jdbcType="DOUBLE"/>
		<result column="card_rule_id" property="cardRuleId" jdbcType="INTEGER"/>
		<result column="account_id" property="accountId" jdbcType="NUMERIC"/>
		<result column="origin_price" property="originPrice" jdbcType="DOUBLE"/>
		<result column="out_stock" property="outStock" jdbcType="INTEGER"/>
		<result column="discount_card_id" property="discountCardId" jdbcType="INTEGER"/>
		<result column="order_sale_type" property="orderSaleType" jdbcType="INTEGER"/>
		<result column="receivable_status" property="receivableStatus" jdbcType="SMALLINT"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="invoice_status" property="invoiceStatus" jdbcType="TINYINT"/>
		<result column="financial_invoice_id" property="financialInvoiceId" jdbcType="NUMERIC"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="operate_id" property="operateId" jdbcType="INTEGER"/>
		<result column="order_pay_type" property="orderPayType" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="ordersColumns">
          t.order_id,
          t.order_no,
          t.m_id,
          t.order_time,
          t.type,
          t.status,
          t.delivery_fee,
          t.total_price,
          t.remark,
          t.confirm_time,
          t.area_name,
          t.out_times,
          t.discount_type,
          t.out_times_fee,
          t.area_no,
          t.m_size,
          t.direct,
          t.sku_show,
          t.red_pack_amount,
          t.card_rule_id,
          t.account_id,
          t.origin_price,
          t.out_stock,
          t.discount_card_id,
          t.order_sale_type,
          t.receivable_status,
          t.admin_id,
          t.invoice_status,
          t.financial_invoice_id,
          t.update_time,
          t.operate_id,
          t.order_pay_type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="orderId != null">
                AND t.order_id = #{orderId}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="orderTime != null">
                AND t.order_time = #{orderTime}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="deliveryFee != null">
                AND t.delivery_fee = #{deliveryFee}
            </if>
			<if test="totalPrice != null">
                AND t.total_price = #{totalPrice}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
			<if test="confirmTime != null">
                AND t.confirm_time = #{confirmTime}
            </if>
			<if test="areaName != null and areaName !=''">
                AND t.area_name = #{areaName}
            </if>
			<if test="outTimes != null">
                AND t.out_times = #{outTimes}
            </if>
			<if test="discountType != null">
                AND t.discount_type = #{discountType}
            </if>
			<if test="outTimesFee != null">
                AND t.out_times_fee = #{outTimesFee}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
			<if test="mSize != null and mSize !=''">
                AND t.m_size = #{mSize}
            </if>
			<if test="direct != null">
                AND t.direct = #{direct}
            </if>
			<if test="skuShow != null">
                AND t.sku_show = #{skuShow}
            </if>
			<if test="redPackAmount != null">
                AND t.red_pack_amount = #{redPackAmount}
            </if>
			<if test="cardRuleId != null">
                AND t.card_rule_id = #{cardRuleId}
            </if>
			<if test="accountId != null">
                AND t.account_id = #{accountId}
            </if>
			<if test="originPrice != null">
                AND t.origin_price = #{originPrice}
            </if>
			<if test="outStock != null">
                AND t.out_stock = #{outStock}
            </if>
			<if test="discountCardId != null">
                AND t.discount_card_id = #{discountCardId}
            </if>
			<if test="orderSaleType != null">
                AND t.order_sale_type = #{orderSaleType}
            </if>
			<if test="receivableStatus != null">
                AND t.receivable_status = #{receivableStatus}
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="invoiceStatus != null">
                AND t.invoice_status = #{invoiceStatus}
            </if>
			<if test="financialInvoiceId != null">
                AND t.financial_invoice_id = #{financialInvoiceId}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="operateId != null">
                AND t.operate_id = #{operateId}
            </if>
			<if test="orderPayType != null">
                AND t.order_pay_type = #{orderPayType}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="orderTime != null">
                    t.order_time = #{orderTime},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="deliveryFee != null">
                    t.delivery_fee = #{deliveryFee},
                </if>
                <if test="totalPrice != null">
                    t.total_price = #{totalPrice},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
                <if test="confirmTime != null">
                    t.confirm_time = #{confirmTime},
                </if>
                <if test="areaName != null">
                    t.area_name = #{areaName},
                </if>
                <if test="outTimes != null">
                    t.out_times = #{outTimes},
                </if>
                <if test="discountType != null">
                    t.discount_type = #{discountType},
                </if>
                <if test="outTimesFee != null">
                    t.out_times_fee = #{outTimesFee},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="mSize != null">
                    t.m_size = #{mSize},
                </if>
                <if test="direct != null">
                    t.direct = #{direct},
                </if>
                <if test="skuShow != null">
                    t.sku_show = #{skuShow},
                </if>
                <if test="redPackAmount != null">
                    t.red_pack_amount = #{redPackAmount},
                </if>
                <if test="cardRuleId != null">
                    t.card_rule_id = #{cardRuleId},
                </if>
                <if test="accountId != null">
                    t.account_id = #{accountId},
                </if>
                <if test="originPrice != null">
                    t.origin_price = #{originPrice},
                </if>
                <if test="outStock != null">
                    t.out_stock = #{outStock},
                </if>
                <if test="discountCardId != null">
                    t.discount_card_id = #{discountCardId},
                </if>
                <if test="orderSaleType != null">
                    t.order_sale_type = #{orderSaleType},
                </if>
                <if test="receivableStatus != null">
                    t.receivable_status = #{receivableStatus},
                </if>
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="invoiceStatus != null">
                    t.invoice_status = #{invoiceStatus},
                </if>
                <if test="financialInvoiceId != null">
                    t.financial_invoice_id = #{financialInvoiceId},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="operateId != null">
                    t.operate_id = #{operateId},
                </if>
                <if test="orderPayType != null">
                    t.order_pay_type = #{orderPayType},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="ordersResultMap" >
        SELECT <include refid="ordersColumns" />
        FROM orders t
		WHERE t.order_id = #{orderId}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.OrdersQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.OrdersEntity" >
        SELECT
            t.order_id orderId,
            t.order_no orderNo,
            t.m_id mId,
            t.order_time orderTime,
            t.type type,
            t.status status,
            t.delivery_fee deliveryFee,
            t.total_price totalPrice,
            t.remark remark,
            t.confirm_time confirmTime,
            t.area_name areaName,
            t.out_times outTimes,
            t.discount_type discountType,
            t.out_times_fee outTimesFee,
            t.area_no areaNo,
            t.m_size mSize,
            t.direct direct,
            t.sku_show skuShow,
            t.red_pack_amount redPackAmount,
            t.card_rule_id cardRuleId,
            t.account_id accountId,
            t.origin_price originPrice,
            t.out_stock outStock,
            t.discount_card_id discountCardId,
            t.order_sale_type orderSaleType,
            t.receivable_status receivableStatus,
            t.admin_id adminId,
            t.invoice_status invoiceStatus,
            t.financial_invoice_id financialInvoiceId,
            t.update_time updateTime,
            t.operate_id operateId,
            t.order_pay_type orderPayType
        FROM orders t
        <include refid="whereColumnBySelect" />
            ORDER BY t.order_id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.OrdersQueryParam" resultMap="ordersResultMap" >
        SELECT <include refid="ordersColumns" />
        FROM orders t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectByOrderyNo" resultMap="ordersResultMap">
        SELECT <include refid="ordersColumns" />
        FROM orders t
        WHERE t.order_no = #{orderNo}
    </select>
    <select id="selectTotalPriceByMonth" parameterType="net.summerfarm.manage.domain.order.param.query.OrdersQueryParam" resultType="java.math.BigDecimal">
        SELECT SUM(total_price) totalPrice
        FROM orders
        WHERE confirm_time <![CDATA[>=]]> #{startTime}
          AND confirm_time <![CDATA[<]]> #{endTime}
          AND `status` = 6
          AND m_id = #{mId}
    </select>

    <select id="batchGetOrderNos" resultMap="ordersResultMap">
        SELECT <include refid="ordersColumns" />
        FROM orders t
        WHERE t.order_no in
        <foreach collection="orderNos" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectCountByMId" resultType="java.lang.Integer">
        SELECT count(1) totalOrders
        from orders t
        WHERE t.type != 10 and t.status = 6 and t.m_id= #{mId}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.Orders" keyProperty="orderId" useGeneratedKeys="true">
        INSERT INTO orders
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="orderId != null">
				  order_id,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="orderTime != null">
				  order_time,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="deliveryFee != null">
				  delivery_fee,
              </if>
              <if test="totalPrice != null">
				  total_price,
              </if>
              <if test="remark != null">
				  remark,
              </if>
              <if test="confirmTime != null">
				  confirm_time,
              </if>
              <if test="areaName != null">
				  area_name,
              </if>
              <if test="outTimes != null">
				  out_times,
              </if>
              <if test="discountType != null">
				  discount_type,
              </if>
              <if test="outTimesFee != null">
				  out_times_fee,
              </if>
              <if test="areaNo != null">
				  area_no,
              </if>
              <if test="mSize != null">
				  m_size,
              </if>
              <if test="direct != null">
				  direct,
              </if>
              <if test="skuShow != null">
				  sku_show,
              </if>
              <if test="redPackAmount != null">
				  red_pack_amount,
              </if>
              <if test="cardRuleId != null">
				  card_rule_id,
              </if>
              <if test="accountId != null">
				  account_id,
              </if>
              <if test="originPrice != null">
				  origin_price,
              </if>
              <if test="outStock != null">
				  out_stock,
              </if>
              <if test="discountCardId != null">
				  discount_card_id,
              </if>
              <if test="orderSaleType != null">
				  order_sale_type,
              </if>
              <if test="receivableStatus != null">
				  receivable_status,
              </if>
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="invoiceStatus != null">
				  invoice_status,
              </if>
              <if test="financialInvoiceId != null">
				  financial_invoice_id,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="operateId != null">
				  operate_id,
              </if>
              <if test="orderPayType != null">
				  order_pay_type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="orderId != null">
				#{orderId,jdbcType=NUMERIC},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="orderTime != null">
				#{orderTime,jdbcType=TIMESTAMP},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="status != null">
				#{status,jdbcType=SMALLINT},
              </if>
              <if test="deliveryFee != null">
				#{deliveryFee,jdbcType=DOUBLE},
              </if>
              <if test="totalPrice != null">
				#{totalPrice,jdbcType=DOUBLE},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
              <if test="confirmTime != null">
				#{confirmTime,jdbcType=TIMESTAMP},
              </if>
              <if test="areaName != null">
				#{areaName,jdbcType=VARCHAR},
              </if>
              <if test="outTimes != null">
				#{outTimes,jdbcType=INTEGER},
              </if>
              <if test="discountType != null">
				#{discountType,jdbcType=INTEGER},
              </if>
              <if test="outTimesFee != null">
				#{outTimesFee,jdbcType=DOUBLE},
              </if>
              <if test="areaNo != null">
				#{areaNo,jdbcType=INTEGER},
              </if>
              <if test="mSize != null">
				#{mSize,jdbcType=VARCHAR},
              </if>
              <if test="direct != null">
				#{direct,jdbcType=INTEGER},
              </if>
              <if test="skuShow != null">
				#{skuShow,jdbcType=INTEGER},
              </if>
              <if test="redPackAmount != null">
				#{redPackAmount,jdbcType=DOUBLE},
              </if>
              <if test="cardRuleId != null">
				#{cardRuleId,jdbcType=INTEGER},
              </if>
              <if test="accountId != null">
				#{accountId,jdbcType=NUMERIC},
              </if>
              <if test="originPrice != null">
				#{originPrice,jdbcType=DOUBLE},
              </if>
              <if test="outStock != null">
				#{outStock,jdbcType=INTEGER},
              </if>
              <if test="discountCardId != null">
				#{discountCardId,jdbcType=INTEGER},
              </if>
              <if test="orderSaleType != null">
				#{orderSaleType,jdbcType=INTEGER},
              </if>
              <if test="receivableStatus != null">
				#{receivableStatus,jdbcType=SMALLINT},
              </if>
              <if test="adminId != null">
				#{adminId,jdbcType=INTEGER},
              </if>
              <if test="invoiceStatus != null">
				#{invoiceStatus,jdbcType=TINYINT},
              </if>
              <if test="financialInvoiceId != null">
				#{financialInvoiceId,jdbcType=NUMERIC},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="operateId != null">
				#{operateId,jdbcType=INTEGER},
              </if>
              <if test="orderPayType != null">
				#{orderPayType,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.Orders" >
        UPDATE orders t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.order_id = #{orderId,jdbcType=NUMERIC}
        </where>
    </update>
    <update id="updateSelectiveByOrderNo" parameterType="net.summerfarm.manage.infrastructure.model.order.Orders" >
        UPDATE orders t
        <include refid="whereColumnByUpdate"></include>
        where t.order_no = #{orderNo,jdbcType=VARCHAR}
    </update>


    <!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.Orders" >
        DELETE FROM orders t
		WHERE t.order_id = #{orderId,jdbcType=NUMERIC}
    </delete>


    <select id="queryTimingOrderProxySaleNoWarehouseSkuTotalNum" resultType="net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject">
        select c.store_no as storeNo,
               oi.sku     as sku,
               ifnull(sum(oi.amount), 0) as quantity
        from orders o
                 inner join merchant m on m.m_id = o.m_id
                 inner join order_item oi on o.order_no = oi.order_no and oi.sku != 'DF001TD0001'
                inner join inventory i on i.sub_type = 1 and i.sku = oi.sku
                inner join contact c on c.m_id = m.m_id and c.is_default = 1 and c.`status` = 1
            and c.store_no = #{storeNo}
        where o.type = 1
          and o.status = 3
          and o.`area_no` in (SELECT `area_no` FROM `fence` f where f.`store_no` = #{storeNo} and f.`status` = 0)
        group by c.store_no, oi.sku
    </select>

    <select id="queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum" resultType="net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject">
        select dp.order_store_no as storeNo,
               oi.sku as sku,
               ifnull(sum(dp.quantity), 0) as quantity
        from orders o
                 inner join delivery_plan dp on dp.order_no = o.order_no
            and dp.order_store_no = #{storeNo}
                 inner join order_item oi on o.order_no = oi.order_no and oi.sku != 'DF001TD0001'
         inner join inventory i on i.sub_type = 1 and i.sku = oi.sku
        where o.type = 1
          and o.status = 3
          and o.`area_no` in (SELECT `area_no` FROM `fence` f where f.`store_no` = #{storeNo} and f.`status` = 0)
          and dp.status in (2, 3, 6)
        group by dp.order_store_no, oi.sku
    </select>

    <select id="queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum" resultType="net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject">
        select c.store_no as storeNo, t.sku as sku, ifnull(sum(asp.quantity), 0) as quantity
        from orders o
                 inner join after_sale_order t on t.`order_no` = o.`order_no` and t.`suit_id` = 0
            and t.`deliveryed` = 0
                 inner join inventory i on i.sub_type = 1 and i.sku = t.sku
                 inner join `after_sale_proof` asp on asp.after_sale_order_no = t.`after_sale_order_no`
            and asp.status = 2
                 inner join merchant m on m.m_id = o.m_id
                 inner join contact c on c.m_id = m.m_id and c.is_default = 1 and c.`status` = 1
            and c.store_no = #{storeNo}
        where o.type = 1
          and o.status = 3
        group by c.store_no, t.sku
    </select>

    <select id="getPendingOrders" resultMap="ordersResultMap">
        SELECT o.order_no, o.type, o.order_id
        FROM orders o
        where order_time <![CDATA[<]]> DATE_SUB(CURDATE(), INTERVAL 10 DAY) and `status` = 3
        <if test="orderNo != null">
            AND o.order_no = #{orderNo}
        </if>
        ORDER BY o.order_id asc
        limit #{pageStart}, #{pageSize}
    </select>
</mapper>